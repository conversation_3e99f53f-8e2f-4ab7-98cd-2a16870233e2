/**
 * 悬浮在网页上的挂件
 */
module.exports = {
  THEME_SWITCH: process.env.NEXT_PUBLIC_THEME_SWITCH || false, // 是否显示切换主题按钮
  
  // Chatbase 是否显示chatbase机器人 https://www.chatbase.co/
  CHATBASE_ID: process.env.NEXT_PUBLIC_CHATBASE_ID || null,
  
  // 新增：我们自己的Gemini聊天机器人配置
  GEMINI_CHAT_ENABLED: process.env.NEXT_PUBLIC_GEMINI_CHAT_ENABLED === 'true' || false,
  GEMINI_API_URL: process.env.NEXT_PUBLIC_GEMINI_API_URL || null,
  
  // WebwhizAI 机器人 @see https://github.com/webwhiz-ai/webwhiz
  WEB_WHIZ_ENABLED: process.env.NEXT_PUBLIC_WEB_WHIZ_ENABLED || false, // 是否显示
  WEB_WHIZ_BASE_URL:
    process.env.NEXT_PUBLIC_WEB_WHIZ_BASE_URL || 'https://api.webwhiz.ai', // 可以自建服务器
  WEB_WHIZ_CHAT_BOT_ID: process.env.NEXT_PUBLIC_WEB_WHIZ_CHAT_BOT_ID || null, // 在后台获取ID
  DIFY_CHATBOT_ENABLED: process.env.NEXT_PUBLIC_DIFY_CHATBOT_ENABLED || false,
  DIFY_CHATBOT_BASE_URL: process.env.NEXT_PUBLIC_DIFY_CHATBOT_BASE_URL || '',
  DIFY_CHATBOT_TOKEN: process.env.NEXT_PUBLIC_DIFY_CHATBOT_TOKEN || '',

  // 悬浮挂件
  WIDGET_PET: process.env.NEXT_PUBLIC_WIDGET_PET || true, // 是否显示宠物挂件
  WIDGET_PET_LINK:
    process.env.NEXT_PUBLIC_WIDGET_PET_LINK ||
    'https://live2d.wobshare.us.kg/model/Violet/14.json', // 挂件模型地址 @see https://github.com/xiazeyu/live2d-widget-models
  WIDGET_PET_SWITCH_THEME:
    process.env.NEXT_PUBLIC_WIDGET_PET_SWITCH_THEME || false, // 点击宠物挂件切换博客主题

  SPOILER_TEXT_TAG: process.env.NEXT_PUBLIC_SPOILER_TEXT_TAG || '', // Spoiler文本隐藏功能，如Notion中 [sp]希望被spoiler的文字[sp]，填入[sp] 即可

  // 音乐播放插件
  MUSIC_PLAYER: process.env.NEXT_PUBLIC_MUSIC_PLAYER || true, // 是否使用音乐播放插件
  MUSIC_PLAYER_VISIBLE: process.env.NEXT_PUBLIC_MUSIC_PLAYER_VISIBLE || true, // 是否在左下角显示播放和切换，如果使用播放器，打开自动播放再隐藏，就会以类似背景音乐的方式播放，无法取消和暂停
  MUSIC_PLAYER_AUTO_PLAY:
    process.env.NEXT_PUBLIC_MUSIC_PLAYER_AUTO_PLAY || false, // 是否自动播放，不过自动播放时常不生效（移动设备不支持自动播放）
  MUSIC_PLAYER_LRC_TYPE: process.env.NEXT_PUBLIC_MUSIC_PLAYER_LRC_TYPE || '3', // 歌词显示类型，可选值： 3 | 1 | 0（0：禁用 lrc 歌词，1：lrc 格式的字符串，3：lrc 文件 url）（前提是有配置歌词路径，对 meting 无效）
  MUSIC_PLAYER_CDN_URL:
    process.env.NEXT_PUBLIC_MUSIC_PLAYER_CDN_URL ||
    'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js',
  MUSIC_PLAYER_ORDER: process.env.NEXT_PUBLIC_MUSIC_PLAYER_ORDER || 'list', // 默认播放方式，顺序 list，随机 random
  MUSIC_PLAYER_AUDIO_LIST: [
    // 示例音乐列表。除了以下配置外，还可配置歌词，具体配置项看此文档 https://aplayer.js.org/#/zh-Hans/
    {
      name: '若是月亮还没来',
      artist: '王宇宙Leto / 乔浚丞',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/若是月亮还没来 - 王宇宙Leto、乔浚丞.mp3',
      cover:
        'https://imgessl.kugou.com/stdmusic/20240122/20240122171638637928.jpg',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/若月亮没来 (若是月亮还没来)-王宇宙Leto_乔浚丞.lrc'
    },
    {
      name: '天空没有极限',
      artist: 'G.E.M. 邓紫棋',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/天空没有极限 - G.E.M. 邓紫棋.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/天空没有极限-G.E.M.邓紫棋.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220811/20220811174703769289.jpg'
    },
    {
      name: '星空旅行者',
      artist: '亚久津',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星空旅行者 - 亚久津.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星空旅行者 - 亚久津.lrc',
      cover:
        'https://p1.music.126.net/r-pUiyDSZyU3ZiituZXvfw==/109951168229070284.jpg?param=177y177'
    },
    {
      name: '未末(写给00后致以童年)',
      artist: 'SwevenG骨子哥',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/未末(写给00后致以童年)_MQ - SwevenG骨子哥.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/未末(写给00后致以童年) - SwevenG骨子哥_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/vPCu8b7A5Fabjk5_OyXhHQ==/109951167917209214.jpg?param=130y130'
    },
    {
      name: '海街寺庙',
      artist: '七月的星期七',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/海街寺庙_MQ - 吴垚滔（七月的星期七）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/海街寺庙-吴垚滔.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230929/20230929191900346189.jpg'
    },
    {
      name: '点燃生命的光',
      artist: '何婧',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/点燃生命的光 - 何婧.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/点燃生命的光 - 何婧_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230630/20230630172218568654.jpg'
    },
    {
      name: 'Fight ',
      artist: 'BeatBrothers',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Fight - BeatBrothers.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/BeatBrothers《Fight》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/fHSaNDH8KR3i1LmhGCnYvQ==/109951167105793243.jpg?param=130y130'
    },
    {
      name: 'Faded Slowed V4',
      artist: 'Ada DJ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/DJ Faded Slowed V4 - Ada DJ.mp3',
      cover:
        'https://p2.music.126.net/hpNqEh7tm67YSoO2v0PvsA==/109951168607628485.jpg?param=130y130'
    },
    {
      name: 'Normal No More ',
      artist: 'Tysm',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Normal No More - Tysm.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/TYSM《Normal No More》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/s3mKaXDECHZlxi43d7bkCA==/109951164905500417.jpg?param=130y130'
    },
    {
      name: 'Clsr (Aash Mehta Flip)',
      artist: 'Hushi',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Clsr - Hushi.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Hushi《Clsr》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/Q5golrdqVB5NpuBK-W9gKg==/109951167709302469.jpg?param=140y140'
    },
    {
      name: 'Friendships (Lost My Love) ',
      artist: 'Pascal Letoublon,Leony',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Friendships (Lost My Love) - Pascal Letoublon,Leony.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Pascal Letoublon&Leony《Friendships (Lost My Love)》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/Zebam9lT2cRZtt2lI-jDUw==/109951166367955352.jpg?param=130y130'
    },
    {
      name: 'Catch My Breath ',
      artist: 'xxxCr3',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Catch My Breath（降调） - xxxCr3.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/MAMUSUONA《Catch My Breath（降调）》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/lqUJW4EkNk-ldB9Y5j0mHw==/2385940232315172.jpg?param=130y130'
    },
    {
      name: 'Anima Libera',
      artist: 'Fmoel',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Anima Libera - Fmoel.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Fmoel《Anima Libera》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231101/20231101125403972043.jpg'
    },
    {
      name: 'Doopravdy',
      artist: 'Dark,Rain',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Doopravdy - Dark,Rain.mp3',
      lrc: '',
      cover:
        'https://p1.music.126.net/DCjSv2SrhiqfSkO_UzZxaQ==/109951168928562717.jpg?param=130y130'
    },
    {
      name: 'dead eyes (DJ Thailand版)',
      artist: 'Powfu、Ouse ツ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/dead eyes (DJ Thailand版) - Powfu、Ouse ツ.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Powfu&DJ Thailand《Dead Eyes》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/ju-CS-_FCh9e_Jd0vZHn-Q==/109951169286872275.jpg?param=130y130'
    },
    {
      name: 'Wanted you',
      artist: '阿索阿索',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Wanted you - 阿索阿索.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿索阿索《Wanted you》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230817/20230817161433174992.jpg'
    },
    {
      name: 'Yihuik苡慧 - DJ致你',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2022/80daab49e1944e33.m4a',
      cover:
        'https://p2.music.126.net/VDmN2dNpIFu4gTv4bZe6KQ==/109951166254691365.jpg?param=177y177'
    },
    {
      name: 'Padeen',
      artist: '网络歌手',
      url: 'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/37875d.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/71f568fa.jpg'
    },
    {
      name: '张叶蕾 - DJ还是分开',
      artist: 'DJ57',
      url: 'https://mp4.djuu.com/c115/88926/117/2022/ad2a977586793858.m4a',
      cover:
        'https://p1.music.126.net/tH5FdakJX47uy8mlM0GMWQ==/19218363741925314.jpg?param=130y130'
    },
    {
      name: '刘增瞳 - DJ多想留在你身边',
      artist: 'DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2023/4f2f5f58c85c997d.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20200620/20200620074827326009.jpg'
    },
    {
      name: '贺敬轩 - DJ罗曼蒂克的爱情',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2022/03857b877430ebd7.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20230210/20230210143310329219.jpg'
    },
    {
      name: 'DJ那个女孩',
      artist: 'Dj小天',
      url: 'https://mp4.djuu.com/c4/26/2024/b377e5a72056c606.m4a',
      cover:
        'https://img.djuu.com/cover/201808/7424ca.jpg'
    },
    {
      name: '陈粒 - DJ虚拟',
      artist: 'Dj小星',
      url: 'https://mp4.djuu.com/c4/26/2023/bb5ae001b7ea0dcb.m4a',
      cover:
        'https://p2.music.126.net/sPTos4kU_3_z7CPcLV_vAw==/109951169284283186.jpg?param=130y130'
    },
    {
      name: 'Kirsty刘瑾睿 - DJ若把你',
      artist: 'Dj小蒋',
      url: 'https://mp4.djuu.com/c4/26/2023/ff85035eff007701.m4a',
      cover:
        'https://p2.music.126.net/4M7fmZIEX8UN3aiW7xXn1g==/109951168500893877.jpg?param=177y177'
    },
    {
      name: '张大蕾 - DJ日不落',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2021/26cbadeddb2a53e9.m4a',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200831/20200831163012470851.jpg'
    },
    {
      name: '阿刁哇 - DJ外婆的澎湖湾',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2022/b79ba70077566f86.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20210806/20210806173928815991.jpg'
    },
    {
      name: 'Dmc New Years Eve Monsterjam Vol.4',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/97/2017/f8328ac804a1622e.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/970d23c2.jpg'
    },
    {
      name: 'ProgHouse风格热播单曲串烧v3',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/98/2024/83d98ca2a4ede708.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/5e75d23d.jpg'
    },
    {
      name: '精选慢嗨节奏车载串烧V1',
      artist: 'DJ小庆',
      url: 'https://mp4.djuu.com/c2/16/2021/05bbb38f5c7bf716.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/557e80c3.jpg'
    },
    {
      name: '关于你的回忆 DJ串烧',
      artist: 'Dj阿罗',
      url: 'https://mp4.djuu.com/c2/16/2023/9b200505f8640380.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/76fed753.jpg'
    },
    {
      name: 'FunkyHouse风格上头迷幻太空串烧',
      artist: 'DJ小姚',
      url: 'https://mp4.djuu.com/c2/17/2024/dfdc1e6a362e81c9.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/ce8b9c5c.jpg'
    },
    {
      name: 'LakHouse风格一千年以后说爱你',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/16/2024/37875d5e2dc3cf93.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/f8c7f0c9.jpg'
    },
    {
      name: '精选热播车载DJ串烧',
      artist: 'DJ柚仔',
      url: 'https://mp4.djuu.com/c2/16/2023/15660bb912500c6b.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/48b0d0a9.jpg'
    },
    {
      name: 'FunkyHouse太空旋侓DJ串烧',
      artist: 'DJ杰仔',
      url: 'https://mp4.djuu.com/c2/17/2023/00b0b1c69f47c30d.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/e8b53c24.jpg'
    },
    {
      name: '抖音流行 DJ串烧',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/16/2024/48f743daeb837c40.m4a',
      cover:
        'https://img.djuu.com/upload/hot/202310/3aa827.jpg'
    },
    {
      name: '国外Deep Club House串烧',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/97/2017/8319c8717d89bf62.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/11a62668.jpg'
    },
    {
      name: 'ProgHouse新旧流行DJ慢摇串烧',
      artist: 'DJ情白',
      url: 'https://mp4.djuu.com/c2/16/2024/9126da1e7f747571.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/bf43b99e.jpg'
    },
    {
      name: '如果说你是海上的烟火 DJ越鼓串烧',
      artist: 'Dj钢仔',
      url: 'https://mp4.djuu.com/c2/16/2023/83e2210c65611211.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/0f6ee318.jpg'
    },
    {
      name: '精选2024-柔歌车载串烧',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c3/18/2024/d19e7a41f4268d68.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/5559e55b.jpg'
    },
    {
      name: 'Take Me Hand',
      artist: 'DAISHI DANCE / Cécile Corbel',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Take Me Hand - DAISHI DANCE,Cécile Corbel.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/DAISHI DANCE&Cécile Corbel《Take Me Hand》[Mp3_Lrc]2.lrc',
      cover:
        'https://p2.music.126.net/AoVFMKkLVMOmTMo_A6x02g==/109951163403515653.jpg?param=130y130'
    },
    {
      name: 'Take Me Hand',
      artist: '阮言Ruany',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Take Me Hand - 阮言Ruany.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阮言Ruany《Take Me Hand》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221118/20221118163053460817.jpg'
    },
    {
      name: 'Celebrity  ',
      artist: 'IU',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Celebrity - IU.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/IU《Celebrity》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/5G0jdorNO1vRvJDys7kESw==/109951165669210873.jpg?param=130y130'
    },
    {
      name: 'Fool For You',
      artist: 'Kastra',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Fool For You - Kastra.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Kastra《Fool For You》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220906/20220906131324119988.jpg'
    },
    {
      name: 'Walls ',
      artist: 'Ruben',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Walls - Ruben.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Ruben《Walls》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/00z2SMwPfsHq40D_GLRupg==/109951163125534304.jpg?param=130y130'
    },
    {
      name: 'By Your Side',
      artist: 'Jonas Blue,RAYE',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/By Your Side - Jonas Blue,RAYE.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Jonas Blue&RAYE《By Your Side》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/uVgFabWFqhprOAA-bsfJ9A==/18795051766390200.jpg?param=130y130'
    },
    {
      name: 'Faraway (Main version) ',
      artist: 'Gala Rizzatto',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Faraway - Gala.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Gala[欧美]《Faraway (Main Version)》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/HGsjGwsDmhTB365ruWE0nA==/109951169868071557.jpg?param=130y130'
    },
    {
      name: 'Not Angry',
      artist: 'Chris James',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Not Angry - Chris James.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Chris James《Not Angry》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/_X98VQMYCxLlw7lCCZlg9A==/109951170122627159.jpg?param=130y130'
    },
    {
      name: 'So Far Away ',
      artist: 'Martin Garrix/Guetta/Jamie Scott /Romy Dya',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/So Far Away - Martin Garrix、David Guetta、Jamie Scott、Romy Dya.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Martin Garrix&David Guetta&Jamie Scott&Romy Dya《So Far Away》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/bDdwz0zd-BGYpel1QEU2RA==/109951165983886039.jpg?param=130y130'
    },
    {
      name: 'All 4 Nothing (I\'m So In Love)',
      artist: 'Lauv',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/All 4 Nothing (I\'m So In Love) - Lauv.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/All 4 Nothing (I\'m So In Love) - Lauv.lrc',
      cover:
        'https://p1.music.126.net/rdOH6YgFt5bUM_qTON0bSw==/109951167748859276.jpg?param=130y130'
    },
    {
      name: 'Mood',
      artist: 'DJ Dark,Mentol',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Mood - DJ Dark、Mentol.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/DJ Dark&Mentol《Mood》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/UwwYCKLNfRD-NxkrW7VrqQ==/109951168721096171.jpg?param=177y177'
    },
    {
      name: 'Leyla ',
      artist: 'Mesto',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Leyla - Mesto.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Mesto《Leyla》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/oMPkNVC2TxdlSFoxFAH9-A==/109951164852484605.jpg?param=130y130'
    },
    {
      name: 'Cruel Summer ',
      artist: 'Taylor Swift',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Cruel Summer - Taylor Swift.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Taylor Swift《Cruel Summer》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/CJ52j-pEV3jHDcJQ_NBM0Q==/109951168721261222.jpg?param=130y130'
    },
    {
      name: 'Empty Love ',
      artist: 'Lulleaux、Kid Princess',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Empty Love - Lulleaux,Kid Princess.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Lulleaux&Kid Princess《Empty Love》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/xrWSChs7pIOWFjOz5eQIzw==/109951164855840145.jpg?param=130y130'
    },
    {
      name: 'Childhood Dreams ',
      artist: 'Seraphine、Jasmine Clarke、Absofacto',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Childhood Dreams - Seraphine,Jasmine Clarke,Absofacto.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Seraphine&Jasmine Clarke&Absofacto《Childhood Dreams》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/oeHFaJHKYWICwvR4Exidew==/109951168648967564.jpg?param=177y177'
    },
    {
      name: 'Remember Our Summer ',
      artist: 'FrogMonster',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Remember Our Summer - FrogMonster.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/FrogMonster《Remember Our Summer》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/nRKx-2GTCaO4X_tcdE-_bQ==/109951164296813169.jpg?param=130y130'
    },
    {
      name: 'Patience is key in life ',
      artist: 'BY.NEW',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Patience is key in life - BY.NEW.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/BY.NEW《Patience is key in life》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230505/20230505234757406040.jpg'
    },
    {
      name: 'Closer',
      artist: 'The Chainsmokers&Halsey',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/The Chainsmokers_Halsey《Closer》[Mp3_Lrc][www.eev3.com].mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Closer - The Chainsmokers&Halsey_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/2bcwjIFFTiaS6Hg_4AdMJQ==/109951165424074168.jpg?param=130y130'
    },
    {
      name: 'Without Me x Rather Be ',
      artist: 'Kiiiu',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Without Me x Rather Be(0.95X) - Kiiiu.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/WideSide《Losing Game (cover Maximillian)》[Mp3_Lrc].lrc',
      cover:
        'https://tse4-mm.cn.bing.net/th/id/OIP-C.8YNcWGDMaXoO31Cd7HiLlAHaHa?rs=1&pid=ImgDetMain'
    },
    {
      name: 'Closer',
      artist: 'The Chainsmokers / Halsey',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Closer (BGM)_MQ - 一段重复接.mp3',
      lrc: '',
      cover:
        'https://p1.music.126.net/yvVjN9knJw23SeKNqhaexA==/109951165975953380.jpg?param=130y130'
    },
    {
      name: 'BABYDOLL (PHONK)',
      artist: 'Brown、BLCP',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/BABYDOLL (PHONK)_MQ - Brown、BLCP.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/BABYDOLL(PHONK) - Brown&BLCP_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231129/20231129170041122600.jpg'
    },
    {
      name: 'Losing Game',
      artist: 'WideSide',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Losing Game_MQ - WideSide.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Losing Game (cover_ Maximillian) - WideSide_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/O1Mmf6WZMhQzCKrwtr-9-A==/109951166566407842.jpg?param=130y130'
    },
    {
      name: 'Night Cruising',
      artist: 'agraph',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Night Cruising - agraph.mp3',
      lrc: '',
      cover:
        'https://p2.music.126.net/pCidyw5R9wQNIwl1FZ1Xgg==/109951167950134403.jpg?param=130y130'
    },
    {
      name: 'PHUTHON',
      artist: '爱抵万难',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/PHUTHON - 爱抵万难.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/PHUTHON - 爱抵万难_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231213/20231213135132588933.jpg'
    },
    {
      name: 'アルケミラ',
      artist: 'リーガルリリー',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/アルケミラ - リーガルリリー.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/リーガルリリー《アルケミラ》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/Sk1Eds76U2ATIe8jdntHJA==/109951166440813583.jpg?param=130y130'
    },
    {
      name: 'ナイトクルージング ',
      artist: 'Kensuke Ushio',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/ナイトクルージング -Kensuke Ushio.mp3',
      lrc: '',
      cover:
        'https://p2.music.126.net/UZ0pu8ghqcQYDE7-gCsfvQ==/109951165384571011.jpg?param=130y130'
    },
    {
      name: 'Shutterbug ',
      artist: 'Glenna',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Shutterbug - Glenna.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Glenna《Shutterbug》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/fCKEWFj1qWSCPdI8AVCWoQ==/1419469526399516.jpg?param=130y130'
    },
    {
      name: 'Slow Down(0.8X）',
      artist: 'Shake9',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Slow Down(0.8X） - Shake9.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Shake9《Slow Down(0.8X）》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221213/20221213223931990507.jpg'
    },
    {
      name: 'Walk Thru Fire',
      artist: 'Vicetone,Meron Ryan',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Walk Thru Fire - Vicetone,Meron Ryan.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Vicetone&Meron Ryan《Walk Thru Fire》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/SZFvPTbtIWaO2Dz1ytOeAw==/109951163433090567.jpg?param=130y130'
    },
    {
      name: 'Wrap Me In Plastic  ',
      artist: 'CHROMANCE',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Wrap Me In Plastic - CHROMANCE.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/CHROMANCE《Wrap Me In Plastic》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/V25GGA-x5mS1GidOv4C1mA==/109951164279334638.jpg?param=130y130'
    },
    {
      name: 'Musica de Depresion ',
      artist: 'Relaxing Music',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Musica de Depresion - Relaxing Music.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Musica de Depresion-Relaxing Music.lrc',
      cover:
        'https://p1.music.126.net/3SVSGnZv21ifKVu1eFU5ZA==/109951166098407034.jpg?param=130y130'
    },
    {
      name: 'All I Think About ',
      artist: 'Love Harder',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/All I Think About - Love Harder.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Love Harder《All I Think About》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20240208/20240208181503333475.jpg'
    },
    {
      name: 'Once Upon a Time ',
      artist: 'Max Oazo,Moonessa',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Once Upon a Time - Max Oazo,Moonessa.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Max Oazo&Moonessa《Once upon a Time》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/Ih0GQRKYyd_yRaWa-jwmVA==/109951167480965715.jpg?param=130y130'
    },
    {
      name: 'Please Don\'t Go',
      artist: 'Joel Adams',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Please Don\'t Go - Joel Adams.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Please Don’t Go-Joel Adams.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20191018/20191018104607318206.jpg'
    },
    {
      name: 'Outside',
      artist: 'Calvin Harris,Ellie Goulding',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Outside - Calvin Harris,Ellie Goulding.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Calvin Harris&Ellie Goulding《Outside》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/GbpRpAbico1fVt1t6Q-W3A==/109951168719010805.jpg?param=130y130'
    },
    {
      name: 'Ask Yourself ',
      artist: 'Fancy Monster,Monika Santucci',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Ask Yourself - Fancy Monster、Monika Santucci.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Fancy Monster&Monika Santucci《Ask Yourself》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/xayoCxdFBJTnqYs95jVmFw==/109951165546810297.jpg?param=130y130'
    },
    {
      name: 'Is It Just Me',
      artist: 'Sasha Alex Sloan',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Is It Just Me_ - Sasha Alex Sloan.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Sasha Alex Sloan&Charlie Puth《Is It Just Me (feat. Charlie Puth)》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/JhfHKqNP5APgZeO_Cg2pJw==/109951165475807130.jpg?param=130y130'
    },
    {
      name: 'El Condor Pasa',
      artist: 'Leo Rojas',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/El Condor Pasa - Leo Rojas.mp3',
      lrc: '',
      cover:
        'https://p1.music.126.net/UBan1d-BN3zNbzW94s4jUg==/109951165969323508.jpg?param=130y130'
    },
    {
      name: 'Used to Losing You',
      artist: 'Helloworld / JT Roach / Blanke / Luma',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Used to Losing You - HELLOWORLD、JT Roach、Blanke、Luma.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/HELLOWORLD&JT Roach&Blanke&Luma《Used to Losing You》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/YVrEjXe9kc3XCKiIqDg5uQ==/109951168241464795.jpg?param=130y130'
    },
    {
      name: 'Infinity (Slowed & Reverb)',
      artist: 'Kaushal Shekhawat',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Infinity - Kaushal Shekhawat.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Kaushal Shekhawat《Infinity》[Mp3_Lrc].lrc',
      cover:
        'https://is3-ssl.mzstatic.com/image/thumb/Music112/v4/04/74/5e/04745e76-2ca6-20d5-975c-33f1a10fe41d/cover.jpg/1200x1200bf-60.jpg'
    },
    {
      name: 'Darkside ',
      artist: 'Alan Walker,Au,Ra,Tomine Harket ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Darkside - Alan Walker、Au／Ra、Tomine Harket.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Alan Walker&AuRa&Tomine Harket《Darkside》[Mp3_Lrc]2.lrc',
      cover:
        'https://p2.music.126.net/QgA1cIpCY3DN3ov9rrYb_A==/109951165984614060.jpg?param=130y130'
    },
    {
      name: 'Hold On',
      artist: 'ALisa',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Hold On_MQ - ALisa.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/ALisa《Hold On》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/t0G-13az8_lEnrDRRntLWA==/109951166639576807.jpg?param=130y130'
    },
    {
      name: 'Million Days',
      artist: 'Sabai&Hoang&Ridgely',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Million Days - Sabai,Hoang,Claire Ridgley.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Million Days-Sabai&Hoang&Ridgely.lrc',
      cover:
        'https://p1.music.126.net/OCXJq2VEytCN6xIuS7Raag==/109951164630509914.jpg?param=130y130'
    },
    {
      name: 'Shots (Tình yêu và变速版)',
      artist: 'Imagine Dragons、Broiler',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Shots (Tình yêu và变速版) - Imagine Dragons、Broiler.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Imagine Dragons&Broiler《Shots》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/QaSjCh4VWOSrM8L2yZ2LTg==/109951169071616413.jpg?param=130y130'
    },
    {
      name: 'Downed (坠落之地)',
      artist: 'ALisa、ONE STEP',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Downed(坠落之地)_MQ - ALisa、ONE STEP.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Downed（坠落之地）-ALisa&ONE STEP.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210602/20210602212505218148.jpg'
    },
    {
      name: 'She (Radio Edit)',
      artist: 'Groove Coverage',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/She - Groove Coverage.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Groove Coverage《She》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/UezcMjhYK3hLuvod-2RVlQ==/109951164770719928.jpg?param=130y130'
    },
    {
      name: 'Fractures (AZNE Remix)',
      artist: 'AZNE',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Fractures (AZNE Remix) - AZNE.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Fractures-Illenium&Nevve.lrc',
      cover:
        'https://p2.music.126.net/_H76HVLbwHSxK1Jleln43A==/109951167642743495.jpg?param=140y140'
    },
    {
      name: 'In The Shadow Of The Sun',
      artist: 'Professor Green',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/In The Shadow Of The Sun - Professor Green.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Professor Green《In The Shadow Of The Sun》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/v9vylpINJPbjjhfGdWd1Zw==/2534374302157990.jpg?param=130y130'
    },
    {
      name: 'Umbrella(New Ver.)',
      artist: 'Paul Wallen、Gigi Nally',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Paul Wallen,Gigi Nally - Umbrella(New Ver.) - Paul Wallen、Gigi Nally.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Paul Wallen&Gigi Nally《Umbrella》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/Lfn_BGwqprBjWfCdrt6ZOg==/109951166715310163.jpg?param=130y130'
    },
    {
      name: 'You',
      artist: 'Approaching Nirvana',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/You - Approaching Nirvana.mp3',
      lrc: '',
      cover:
        'https://p2.music.126.net/KaDKI96I0yW6HEFZjG01Hg==/109951169265550884.jpg?param=130y130'
    },
    {
      name: 'Wonderful U ',
      artist: 'AGA',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Wonderful U - AGA.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/AGA《Wonderful U》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/eXTty6BSHthg1vN3uwYJOQ==/109951168793223718.jpg?param=130y130'
    },
    {
      name: 'ありがとう… (谢谢…)',
      artist: 'KOKIA',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/ありがとう・・・ - KOKIA.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/KOKIA《ありがとう…》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/Oyai2N1oU4TOAQXitEqlgQ==/1674556209112992.jpg?param=130y130'
    },
    {
      name: '天ノ弱',
      artist: 'こはならむ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/天ノ弱 - こはならむ.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/こはならむ《天ノ弱》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/SNANZcu5PhfvFvM5Fuq_Rw==/109951167964524207.jpg?param=130y130'
    },
    {
      name: '好きだから',
      artist: '『ユイカ』',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/好きだから。 - 『ユイカ』.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/『ユイカ』&れん《好きだから。》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/ozNsc3XPpvMQlHYyU9LVAg==/109951166125448895.jpg?param=130y130'
    },
    {
      name: 'Letting Go',
      artist: '蔡健雅',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/LETTING GO - 蔡健雅.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蔡健雅《Letting Go》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/VTZde5VdBm_u2WH0Pc9HQQ==/109951165561227373.jpg?param=130y130'
    },
    {
      name: '梦的开始很难走',
      artist: '顾逸泽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/梦的开始很难走 - 顾逸泽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/顾逸泽《梦的开始很难走》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/dwzlorFnimQwoWqQHbjdaQ==/109951168933676911.jpg?param=130y130'
    },
    {
      name: '沦陷与心动',
      artist: '王忻辰、杨瑜婷',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/沦陷与心动 - 王忻辰、杨瑜婷.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王忻辰&杨瑜婷《沦陷与心动》.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211028/20211028110851421204.jpg'
    },
    {
      name: '遗忘月光',
      artist: '陆杰awr、袁小葳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/遗忘月光 - 陆杰awr,袁小葳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陆杰awr&袁小葳《遗忘月光》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230602/20230602155531170170.jpg'
    },
    {
      name: '0321',
      artist: '千屿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/0321 - 千屿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/space x《0321》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/240/20230725/20230725165027491.jpg'
    },
    {
      name: '念',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/念 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《念》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210310/20210310154602961435.jpg'
    },
    {
      name: '花再开时',
      artist: '王忻辰、苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花再开时 - 王忻辰、苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王忻辰&苏星婕《花再开时》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20240111/20240111140024572444.jpg'
    },
    {
      name: '相遇太难',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/相遇太难_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/苏星婕《相遇太难》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/24AmQbhjO3qoXENRyKpmKg==/109951168111254692.jpg?param=177y177'
    },
    {
      name: '回不去的何止时间',
      artist: '陈墨一（吖毛）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/回不去的何止时间 - 吖毛.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈墨一（吖毛）《回不去的何止时间》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/pk2C-eXJy_P9i-j74fPRtg==/109951169282108671.jpg?param=130y130'
    },
    {
      name: '那年的风吹不回那个少年',
      artist: '阿辰（阎辰）、单循',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/那年的风吹不回那个少年 - 阿辰（阎辰）、单循.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿辰（阎辰）&单循《那年的风吹不回那个少年》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230622/20230622133507520054.jpg'
    },
    {
      name: '是你',
      artist: '梦然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/是你 - 梦然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/梦然《是你》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/tESNzKJXbzyke4KKoKzm8w==/109951167704212151.jpg?param=130y130'
    },
    {
      name: '星迹',
      artist: '傅梦彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星迹_MQ - 傅梦彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/傅梦彤《星迹》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220326/20220326124204723487.jpg'
    },
    {
      name: '清空 ',
      artist: '王忻辰,苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/清空 - 王忻辰,苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王忻辰&苏星婕《清空》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221205/20221205231005738342.jpg'
    },
    {
      name: '哪里都是你',
      artist: '队长',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/哪里都是你 - 队长.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/队长《哪里都是你》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/lnOnBbP_H-052Hv5ls-QjA==/109951162964628408.jpg?param=130y130'
    },
    {
      name: '晚风吹过落日海',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/晚风吹过落日海 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《晚风吹过落日海》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/LlcqA1nguaL_HbQ0vt0adg==/109951167873648515.jpg?param=130y130'
    },
    {
      name: '风过晚霞',
      artist: '邹秋实',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风过晚霞 - 邹秋实.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/邹秋实《风过晚霞》[Mp3_Lrc].lrc',
      cover:
        'https://p3fx.kgimg.com/stdmusic/240/20220908/20220908175824439998.jpg'
    },
    {
      name: '夏日预售',
      artist: 'LightYear光年',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/夏日预售 - LightYear光年.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/LightYear光年《夏日预售》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/aLFH0Gsr4T-NaXQ3rxsL1A==/109951167222706915.jpg?param=130y130'
    },
    {
      name: '丑时',
      artist: '音阙诗听、王梓钰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/丑时 - 音阙诗听,王梓钰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/音阙诗听&王梓钰《丑时》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210827/20210827170704753730.jpg'
    },
    {
      name: '落日星空 ',
      artist: '赵希予',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落日星空 - 赵希予.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/赵希予《落日星空》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/QWre0f3QzF1c4vPw3TY6tw==/109951168202004218.jpg?param=130y130'
    },
    {
      name: '鲸语',
      artist: '刘至佳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/鲸语 - 刘至佳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/刘至佳《鲸语》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220111/20220111154802548678.jpg'
    },
    {
      name: '星空飞行',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星空飞行 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《星空飞行》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/QLBb3z3mGDwb13CDohr1Ew==/109951165912011546.jpg?param=130y130'
    },
    {
      name: '防线 ',
      artist: '吴瑭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/防线 - 吴瑭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/吴瑭《防线》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210527/20210527175429663564.jpg'
    },
    {
      name: '忏悔录',
      artist: 'KKECHO、那奇沃夫、DR1P',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/忏悔录 - KKECHO、那奇沃夫、DR1P.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/提《忏悔录 那奇沃夫》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/huYun8jQ5N5wKi2Rsa_YPw==/109951167757521194.jpg?param=130y130'
    },
    {
      name: '抬头仰望 别浪费了月亮',
      artist: 'Callie Parsons',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/抬头仰望 别浪费了月亮 - Callie Parsons.mp3',
      lrc: '',
      cover:
        'https://pic1.zhimg.com/v2-9a52f23860fbe76f5cdc47ac6d1454f0_r.jpg'
    },
    {
      name: '微微',
      artist: '傅如乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/微微 - 傅如乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/傅如乔《微微》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/IUO87TLnekKYxGaWg_CbiQ==/109951164839657994.jpg?param=130y130'
    },
    {
      name: '追寻你',
      artist: '王天戈、川青',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/追寻你 - 王天戈,川青.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王天戈&川青《追寻你》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/uIdF3t8Rik7gOv-gZ2NxXQ==/109951166947705387.jpg?param=130y130'
    },
    {
      name: '飞鸟和蝉',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/飞鸟和蝉 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《飞鸟和蝉》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/WwoSbzWLNVPBoh7iXFgerA==/109951165114761150.jpg?param=130y130'
    },
    {
      name: '六月刮起台风',
      artist: '雪球,庄淇文29',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/六月刮起台风 - 雪球,庄淇文29.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/雪球&庄淇玟29《六月刮起台风》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230907/20230907145739444896.jpg'
    }, 
    {
      name: '过往不恋',
      artist: '白色、欧阳朵 - ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/过往不恋 - 白色、欧阳朵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/白色&欧阳朵《过往不恋》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210128/20210128175502400639.jpg'
    },
    {
      name: '别困在十万大山',
      artist: '7paste',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/7paste-别困在十万大山 - 7paste.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/7paste《别困在十万大山》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230317/20230317103141141003.jpg'
    },
    {
      name: '跨年',
      artist: '林哲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/跨年 - 林哲.mp3',
      lrc: '',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230430/20230430165855130200.jpg'
    },
    {
      name: '欲落的樱花',
      artist: '初月',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/欲落的樱花_MQ - 初月.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/初月《欲落的樱花》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230726/20230726194907870032.jpg'
    },
    {
      name: '相拥星空',
      artist: '张洛一 ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/相拥星空 - 张洛一.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/张洛一《相拥星空》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220303/20220303154329708522.jpg'
    },
    {
      name: '星河回望',
      artist: '余又',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星河回望 - 余又.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星河回望-余又.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230601/20230601200247533844.jpg'
    },
    {
      name: '藏刃',
      artist: '甘璐、墨绛',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/藏刃 - 甘璐、墨绛.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/藏刃-甘璐&墨绛.lrc',
      cover:
        'https://p1.music.126.net/BUFVk-MfSQgGfH4tGabbmQ==/109951168111037785.jpg?param=130y130'
    },
    {
      name: '怦然心动',
      artist: '赵方婧、李佳思',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/怦然心动 - 赵方婧、李佳思.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/赵方婧&李佳思《怦然心动》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/l8FbfXGYplbGPxeiEVn55A==/109951168214024154.jpg?param=130y130'
    },
    {
      name: '独到',
      artist: '柏香果吖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/独到 - 柏香果吖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/柏香果吖《独到》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220426/20220426170408476594.jpg'
    },
    {
      name: '如果大雨下一整夜',
      artist: 'Big Cole',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/如果大雨下一整夜 - Big Cole.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Big Cole《如果大雨下一整夜》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/6WcdOzepOepG6qvFBNJFtA==/109951168646936451.jpg?param=177y177'
    },
    {
      name: '命名星辰',
      artist: '风寄、LKer林柯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/命名星辰 - 风寄、LKer林柯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风寄&LKer林柯《命名星辰》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220323/20220323114722575954.jpg'
    },
    {
      name: '遇星 (纯享版)',
      artist: '韩帅（HS）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/遇星 - 韩帅（HS）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/韩帅（HS）《遇星》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230527/20230527163108913497.jpg'
    },
    {
      name: '无人',
      artist: '吴东旭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/无人_MQ - 吴东旭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/吴东旭《无人》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221220/20221220124113362244.jpg'
    },
    {
      name: '从深情到绝情',
      artist: '袁小葳、阿辰（阎辰）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/从深情到绝情 - 袁小葳,阿辰（阎辰）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/袁小葳&阿辰（阎辰）《从深情到绝情》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/WTWyrUjGpwY4L1RwKtRjgw==/109951168886841418.jpg?param=177y177'
    },
    {
      name: '雨是神的烟花',
      artist: '初月',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雨是神的烟花 - 初月.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/初月《雨是神的烟花》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231103/20231103101626830778.jpg'
    },
    {
      name: '那个远方',
      artist: '陈楚生',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/那个远方 - 陈楚生.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈楚生《那个远方》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/0OuSHTLwYv237yyGwVlx_A==/3352410953143847.jpg?param=130y130'
    },
    {
      name: 'Da Da Da (甜妹版)',
      artist: '芊芊龍',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Da Da Da (甜妹版)_MQ - 芊芊龍.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/芊芊龍《Da Da Da(甜妹版)》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/ifjrMa4g6_oV1pe0ruR53A==/109951169432548463.jpg?param=130y130'
    },
    {
      name: '缺氧',
      artist: '轩姨(相信光)',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/缺氧 - 轩姨(相信光).mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/轩姨(相信光)《缺氧》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221008/20221008105215539681.jpg'
    },
    {
      name: '麦田守望者',
      artist: 'Cifer',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/麦田守望者 - Cifer.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Cifer《麦田守望者》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/HKyF46ekI7Gd1JRW9JwKHg==/109951168207468627.jpg?param=177y177'
    },
    {
      name: '妈妈的话',
      artist: 'Zyboy忠宇',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/妈妈的话 - Zyboy忠宇.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Zyboy忠宇《妈妈的话》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220104/20220104141256795835.jpg'
    },
    {
      name: '指纹',
      artist: '杜宣达',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/指纹 - 杜宣达 .mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/杜宣达《指纹》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/kT4AECoc13oVKLEg_-eFMA==/109951166578436098.jpg?param=130y130'
    },
    {
      name: '与我无关',
      artist: '阿冗',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/与我无关 - 阿冗.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/与我无关-阿冗.lrc',
      cover:
        'https://p2.music.126.net/x-jReyGkM5OTKUEtTqXGoA==/109951164597332931.jpg?param=130y130'
    },
    {
      name: '时间的声音',
      artist: 'K.D',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/时间的声音_MQ - K.D.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/K.D《时间的声音》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230428/20230428125205683582.jpg'
    },
    {
      name: 'Electro新东泰车载DJ串烧',
      artist: 'Dj炳仔',
      url: 'https://mp4.djuu.com/c2/98/2024/577073458fef4742.m4a',
      lrc: '',
      cover:
        'https://img.djuu.com/cover/2023928/99248afb.jpg'
    },
    {
      name: 'ProgressiveTrance风格星河之音DJ串烧',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c2/98/2024/b46f5085d1fc319a.m4a',
      lrc: '',
      cover:
        'https://img.djuu.com/cover/2023928/35e11802.jpg'
    },
    {
      name: 'LakHouse音乐女声包房暖场慢摇串烧',
      artist: 'no',
      url: 'https://mp4.djuu.com/c2/16/2024/442a3ee249db1dd7.m4a',
      lrc: '',
      cover:
        'https://img.djuu.com/cover/20231001/761bd16f.jpg'
    },
    {
      name: '蔡健雅 - DJ红色高跟鞋',
      artist: 'DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2023/9181db49330a3112.m4a',
      lrc: '',
      cover:
        'https://img.djuu.com/cover/201807/c346d0.jpg'
    },
    {
      name: 'FunkyHouse我的楼兰那一夜车载串烧',
      artist: 'DJ小姚',
      url: 'https://mp4.djuu.com/c2/16/2024/513943f6d2534c3c.m4a',
      lrc: '',
      cover:
        'https://img.djuu.com/cover/2023928/71f568fa.jpg'
    },
    {
      name: '曲肖冰 - DJ我是真的爱上你',
      artist: 'DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2023/42ec3e4fca0ce4c1.m4a',
      cover:
        'https://p1.music.126.net/3xlx84hJQBbQDC1Hlsk5Ow==/109951168285392637.jpg?param=130y130'
    },
    {
      name: '双笙 (陈元汐) - DJ心做し',
      artist: 'DJ57',
      url: 'https://mp4.djuu.com/c115/88926/117/2022/79f4555a9bd657a8.m4a',
      cover:
        'https://p1.music.126.net/4m79yXLdqFt_0mbx9DjsoA==/109951166279817646.jpg?param=130y130'
    },
    {
      name: '邓紫棋 - DJ再见',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/529879540cd5c896.m4a',
      cover:
        'https://p1.music.126.net/kVwk6b8Qdya8oDyGDcyAVA==/1364493930777368.jpg?param=130y130'
    },
    {
      name: '张紫豪 - DJ可不可以',
      artist: 'Dj炮哥',
      url: 'https://mp4.djuu.com/c4/26/2023/3efcb70921d0eb84.m4a',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20230821/20230821162619428.jpg'
    },
    {
      name: '季彦霖 - DJ多想留在你身边',
      artist: ' DJ小罗',
      url: 'https://mp4.djuu.com/c4/26/2021/292b7a4c7be41d0b.m4a',
      cover:
        'https://img.djuu.com/cover/201905/5dd6a8.jpg'
    },
    {
      name: '邵雨涵 - DJ快乐阿拉蕾',
      artist: '岑溪DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2023/6972a7d09cb5b074.m4a',
      cover:
        'https://img.djuu.com/cover/202206/bef0d7.jpg'
    },
    {
      name: '邓紫棋 - DJ龙卷风',
      artist: 'Dj超',
      url: 'https://mp4.djuu.com/c4/26/2023/5582435d62ef3e4e.m4a',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20230420/20230420151859729808.jpg'
    },
    {
      name: '夏婉安 - DJ泡沫',
      artist: 'Dj京仔',
      url: 'https://mp4.djuu.com/c4/41/2022/8b44d6b96d7ed3a2.m4a',
      cover:
        'https://p1.music.126.net/cxgxFZSh8OJArluwm9SBYw==/109951167970335226.jpg?param=140y140'
    },
    {
      name: 'Taylor Swift - DJLove Story',
      artist: 'Dj小邓',
      url: 'https://mp4.djuu.com/c5/28/2022/58e185db03cf1bf7.m4a',
      cover:
        'https://img.djuu.com/cover/201807/ba2223.jpg'
    },
    {
      name: '崔子格 -DJ卜卦',
      artist: 'Dj小海',
      url: 'https://mp4.djuu.com/c4/26/2024/e3b1740ed78c255d.m4a',
      cover:
        'https://so1.360tres.com/dr/220__/t017a0d2875c5a92330.jpg'
    },
    {
      name: '阿肆 - DJ热爱105℃的你',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/062f8952ad1e8f79.m4a',
      cover:
        'https://p1.music.126.net/6Herq6VjqEM2wJYiML3y4A==/109951166098679116.jpg?param=130y130'
    },
    {
      name: 'Zkaaai - DJ迟来的情话',
      artist: 'DjZr',
      url: 'https://mp4.djuu.com/c4/26/2023/8afb2e4cc9bdc4d0.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20230203/20230203181951243715.jpg'
    },
    {
      name: '小时姑娘',
      artist: '曾有柳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/曾有柳 - 小时姑娘.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小时姑娘&时不语工作室《曾有柳》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220413/20220413191130460290.jpg'
    },
    {
      name: '执迷不悟',
      artist: '王贰浪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/执迷不悟 - 王贰浪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王贰浪《执迷不悟》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220429/20220429140942298153.jpg'
    },
    {
      name: 'De Yang Gatal Gatal',
      artist: '依邦妮,M爷',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/依邦妮、M爷 - De Yang Gatal Gatal.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Lala Widy-De Yang Gatal Gatal（安筱冷 remix）-种一捧玫瑰.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210319/20210319194558114181.jpg'
    },
    {
      name: '记·念',
      artist: '雷雨心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/记·念 - 雷雨心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/记·念-雷雨心.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220307/20220307150405386086.jpg'
    },
    {
      name: '沦陷 ',
      artist: '王靖雯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/沦陷 - 王靖雯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王靖雯《沦陷》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/HVhFgd7tPxzvAhPn22Wlsg==/109951165772379554.jpg?param=130y130'
    },
    {
      name: '靠近一点点',
      artist: '黎林添娇,张颖轩',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/靠近一点点 - 黎林添娇,张颖轩.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/黎林添娇&张颖轩《靠近一点点》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221125/20221125121006916828.jpg'
    },
    {
      name: '这次你真的走了',
      artist: '1个球',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/这次你真的走了 - 1个球.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/1个球《这次你真的走了》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/5nEW-xeunjuEJyIxM318CA==/109951168211022234.jpg?param=130y130'
    },
    {
      name: 'TA',
      artist: '不是花火呀',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/TA - 不是花火呀.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不是花火呀《TA》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210615/20210615183615987259.jpg'
    },
    {
      name: '少年',
      artist: '梦然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/少年 - 梦然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/梦然《少年》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20191110/20191110174405582448.jpg'
    },
    {
      name: '阿拉斯加海湾',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/阿拉斯加海湾 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蓝心羽《阿拉斯加海湾》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/j9P19hOTNbYxLZDJB9bJag==/109951169385715334.jpg?param=130y130'
    },
    {
      name: '听不到你说',
      artist: 'Joysaaaa',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/听不到你说 - Joysaaaa.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Joysaaaa《听不到你说》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220520/20220520210533592143.jpg'
    },
    {
      name: 'ME&YOU',
      artist: '小安FUROSIKI、洛劫 ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/ME&YOU_MQ - 小安FUROSIKI、洛劫.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小安FUROSIKI&洛劫《ME&YOU》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210715/20210715113036933629.jpg'
    },
    {
      name: '故事很短',
      artist: '于冬然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/故事很短 - 于冬然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/于冬然《故事很短》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210702/20210702185456633463.jpg'
    },
    {
      name: '一个人 ',
      artist: '夏婉安',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一个人 - 夏婉安.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/夏婉安《一个人》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20150715/20150715230628433049.jpg'
    },
    {
      name: '非酋',
      artist: '薛黛霏、朱贺',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/非酋 - 薛黛霏、朱贺.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/薛黛霏&朱贺《非酋》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221031/20221031192709984796.jpg'
    },
    {
      name: '你比爱走得匆匆',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/你比爱走得匆匆 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《你比爱走得匆匆》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221207/20221207231847966839.jpg'
    },
    {
      name: 'Fatal love',
      artist: '气质韩',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Fatal love - 气质韩.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/气质韩《Fatal Love》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220417/20220417135322381355.jpg'
    },
    {
      name: '青印 ',
      artist: '张德伊玲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/青印 - 张德伊玲.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/张德伊玲《青印》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220608/20220608164133577428.jpg'
    },
    {
      name: '地铁等待 ',
      artist: '黄泳欣',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/地铁等待 - 黄泳欣.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/黄泳欣《地铁等待》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20190321/20190321175807949872.jpg'
    },
    {
      name: '东方人偶_MQ',
      artist: '兰音Reine',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/兰音Reine_-_东方人偶_MQ_(1)1.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/兰音Reine《东方人偶》[Mp3_Lrc].lrc',
      cover:
        'https://i0.hdslb.com/bfs/archive/e2fe4cddc4faaf008eb4f250d718539e93cfda71.jpg'
    },
    {
      name: '慢慢 ',
      artist: 'Uu',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/慢慢 - Uu.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Uu (刘梦妤)《慢慢》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/AtXJyns4pGTOx1704ALLmQ==/109951165759188468.jpg?param=130y130'
    },
    {
      name: '就让这大雨全都落下',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/就让这大雨全都落下_MQ - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔小乔《就让这大雨全都落下》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/IYpMAjuAAgaYm_UCnhibSQ==/109951168604083402.jpg?param=130y130'
    },
    {
      name: '天亮以前说再见 ',
      artist: '何野 - ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/天亮以前说再见_MQ - 何野.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/何野《天亮以前说再见》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/flYw6Tw2lCtJBbX99DEVkQ==/109951163628395436.jpg?param=130y130'
    },
    {
      name: '追溯海底',
      artist: '尹昔眠,小田音乐社',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/追溯海底 - 尹昔眠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/尹昔眠&小田音乐社《追溯海底》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/2inZjxZimFDvxQmPVna3QA==/109951169169126203.jpg?param=130y130'
    },
    {
      name: '与光同行 ',
      artist: '王梓琪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/与光同行 - 王梓琪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王梓琪《与光同行》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220128/20220128173917397844.jpg'
    },
    {
      name: '偏向 ',
      artist: '添儿呗',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/偏向 - 添儿呗.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/添儿呗《偏向》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220107/20220107094607667345.jpg'
    },
    {
      name: '遗憾也值得',
      artist: '邢凯悦xky、芊、王靖雯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/遗憾也值得 - 王靖雯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王靖雯《遗憾也值得》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/mtbEBxUYM10hB8Ut1QDizA==/109951165547264225.jpg?param=130y130'
    },
    {
      name: '完美演出',
      artist: '艾辰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/完美演出_MQ - 艾辰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/艾辰《完美演出》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211223/20211223160925903698.jpg'
    },
    {
      name: '芊芊',
      artist: '邢凯悦xky',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/芊芊 - 邢凯悦XkY.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/邢凯悦XkY《芊芊》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200918/20200918194606764235.jpg'
    },
    {
      name: '不再联系',
      artist: 'we1旭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/正在播放- 不再联系（伤感版） - we1旭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不再联系（伤感版）-we1旭.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200918/20200918194606764235.jpg'
    },
    {
      name: '路过人间',
      artist: '郁可唯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/路过人间 - 郁可唯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/郁可唯《路过人间》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20190531/20190531180606107717.jpg'
    },
    {
      name: ' 戒 ',
      artist: '夏婉安,杨OK',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/戒 - 夏婉安,杨OK.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/夏婉安&杨OK《戒》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200407/20200407105108800757.jpg'
    },
    {
      name: '走马',
      artist: '曲肖冰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/走马 - 曲肖冰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/曲肖冰《走马》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220330/20220330010801944734.jpg'
    },
    {
      name: 'BINGBIAN病变 ',
      artist: '鞠文娴',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/BINGBIAN病变 (女声版)_MQ - 鞠文娴.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/鞠文娴《BINGBIAN病变》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/4TuBpyxVpbHnj2EMCSnW9w==/109951163176375492.jpg?param=130y130'
    },
    {
      name: '原谅',
      artist: '刘瑞琦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/原谅_MQ - 刘瑞琦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/张玉华《原谅》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200909/20200909132512576557.jpg'
    },
    {
      name: '天空之外',
      artist: '弦子',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/天空之外 - 弦子.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/弦子《天空之外》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/X-Qoq4sZXbxoskLjKEYrLA==/109951166369327695.jpg?param=130y130'
    },
    {
      name: '孤城',
      artist: '白糖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/孤城 - 白糖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/白糖《孤城》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220415/20220415194833647730.jpg'
    },
    {
      name: '我走后',
      artist: '小咪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我走后 - 小咪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小咪《我走后》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230509/20230509113801471896.jpg'
    },
    {
      name: '四季予你',
      artist: '程响',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/四季予你 - 程响.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/程响《四季予你》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/2J6AKSdhMAVPfcf4-ySIxA==/109951168817344792.jpg?param=177y177'
    },
    {
      name: '落单恋人',
      artist: '零一九零贰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落单恋人 - 零一九零贰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/零一九零贰《落单恋人》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20201015/20201015183503209621.jpg'
    },
    {
      name: '爱妃',
      artist: '阿禹ayy',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/爱妃 - 阿禹ayy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿禹ayy《爱妃》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/I8Uffzh3BbTj538BKd0eVA==/8950024650205516.jpg?param=130y130'
    },
    {
      name: '黎明前的黑暗 ',
      artist: '张韶涵,王晰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/黎明前的黑暗 - 张韶涵、王晰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/张韶涵&王晰《黎明前的黑暗》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200221/20200221233706831832.jpg'
    },
    {
      name: '断线',
      artist: 'Shang,lil sophy',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/断线 - Shang,lil sophy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Shang&lil sophy《断线》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/W-4b64hCb_s8OAGROQXgig==/109951162995598984.jpg?param=130y130'
    },
    {
      name: 'Mashup气氛酒吧热播串烧',
      artist: 'Dj小彬',
      url: 'https://mp4.djuu.com/c2/97/2017/58e3ec622cc68c5c.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/755b432c.jpg'
    },
    {
      name: '精选酒吧说唱DJ串烧',
      artist: 'Dj汤咪',
      url: 'https://mp4.djuu.com/c2/97/2017/84ee18af7212bba5.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/d7bb271c.jpg'
    },
    {
      name: '金莎 - DJ爱的魔法',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/261b320f312d4573.m4a',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200315/20200315133708938821.jpg'
    },
    {
      name: '韩安旭 - DJ多幸运',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/c7927da2bc1cbeba.m4a',
      cover:
        'http'
    },
    {
      name: '张妙格 - DJ我期待的不是雪',
      artist: '岑溪DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2023/149f351a844ad6f2.m4a',
      cover:
        'https://p1.music.126.net/vjCp602rW9qZpGwoRn5nxA==/109951169113690642.jpg?param=130y130'
    },
    {
      name: '林怡婕 - DJ坏女孩',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2022/039db02635b166d4.m4a',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20230420/20230420234517259572.jpg'
    },
    {
      name: 'DJ 樱花树下的约定',
      artist: '恒升Dj阿宸',
      url: 'https://mp4.djuu.com/c4/22/2023/8fca73cdadc27c40.m4a',
      cover:
        'https://img.djuu.com/cover/202206/1a3194.jpg'
    },
    {
      name: 'DJ 希望你被这个世界爱着',
      artist: 'Dj豪大大',
      url: 'https://mp4.djuu.com/c4/41/2023/dba2be5d7aebb4f9.m4a',
      cover:
        'https://img.djuu.com/cover/201911/f24de3.jpg'
    },
    {
      name: 'DJ 谁的青春不迷茫',
      artist: 'DJ57',
      url: 'https://mp4.djuu.com/c115/88926/117/2024/9e1794d63b046bf3.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/b1292f2c.jpg'
    },
    {
      name: 'DJ 说好的幸福',
      artist: 'DJ东东',
      url: 'https://mp4.djuu.com/c115/64152/117/2024/18149d5798764ca4.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/178be933.jpg'
    },
    {
      name: '姑娘别哭泣 DJ串烧',
      artist: '湖南DjxGuo',
      url: 'https://mp4.djuu.com/c2/16/2023/9d76bdac28728599.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/8cd458ac.jpg'
    },
    {
      name: '百听不厌高速车载音乐v42 DJ串烧',
      artist: '中山DJ嘉敏',
      url: 'https://mp4.djuu.com/c2/16/2024/995fc7feca739531.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/5a71e1c9.jpg'
    },
    {
      name: '时间从来不语',
      artist: 'DJ阿智',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/时间从来不语 - DJ阿智.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/DJ阿智《时间从来不语》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/C1WyR1EI0PeBWtz8qcaDPA==/109951169162205527.jpg?param=130y130'
    },
    {
      name: '7710',
      artist: '好乐无荒,尹露浠',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/7710 - 好乐无荒,尹露浠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/好乐无荒&尹露浠《7710》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/MeFGDsKbGEBOLSjX0N_JjA==/109951169398705484.jpg?param=130y130'
    },
    {
      name: '这世界是块冰',
      artist: '顾奈',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/这世界是块冰 - 顾奈.mp3',
      lrc: '',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230619/20230619191959101205.jpg'
    },
    {
      name: '直到你的光晕在我黑夜降临',
      artist: '一个小孩',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/直到你的光晕在我黑夜降临 - 一个小孩.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/一个小孩《直到你的光晕在我黑夜降临》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231009/20231009143059593569.jpg'
    },
    {
      name: '第一次见你的我(好慌张)',
      artist: '一个小孩',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/第一次见你的我(好慌张) - 一个小孩.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/一个小孩《第一次见你的我(好慌张)》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20231009/20231009143059593569.jpg'
    },
    {
      name: '潮汐',
      artist: '傅梦彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/潮汐 - 傅梦彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/潮汐 (Natural) (DJ版)-傅梦彤.lrc',
      cover:
        'https://p1.music.126.net/im6sWzE3npCTVcwOwC_WyA==/109951168162930839.jpg?param=130y130'
    },
    {
      name: '等不来花开',
      artist: '傅梦彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/等不来花开 - 傅梦彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/傅梦彤《等不来花开》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220701/20220701170311589848.jpg'
    },
    {
      name: '海洋',
      artist: 'h3R33',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/海洋 - h3R33.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/h3R33《风驶过的声音是》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/gfLR-3TEncp1cOog8oUaFA==/109951168567036973.jpg?param=130y130'
    },
    {
      name: '回不去的何止时间',
      artist: '小猫阿也',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/回不去的何止时间 - 小猫阿也.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小猫阿也《回不去的何止时间》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20231019/20231019180515323782.jpg'
    },
    {
      name: '黎明前的黑暗',
      artist: 'Bater official',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Bater official - 黎明前的黑暗.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Bater official&Bater Offical《黎明前的黑暗（完整版）》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/yakh4zGxU8WUhQNiFvDXOA==/109951164761904321.jpg?param=130y130'
    },
    {
      name: '白雪飘啊飘',
      artist: '鹅老板酱',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/白雪飘啊飘 - 鹅老板酱.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/鹅老板酱《白雪飘啊飘》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20240119/20240119111655199805.jpg'
    },
    {
      name: '被遗忘的海',
      artist: '陆杰awr,李OK',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/被遗忘的海 - 陆杰awr,李OK.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陆杰awr&李OK《被遗忘的海》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230712/20230712160515887254.jpg'
    },
    {
      name: '山间有歌',
      artist: '王子健',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/山间有歌_MQ - 王子健.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王子健《山间有歌》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/6Ay9lYVIyEfEu2pJ9xPnIg==/109951168651042621.jpg?param=130y130'
    },
    {
      name: '醉秦淮',
      artist: '糯米Nomi,赵希予',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/醉秦淮 - 糯米Nomi,赵希予.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/糯米Nomi&赵希予《醉秦淮》[Mp3_Lrc].lrc',
      cover:
        'https://i.ytimg.com/vi/DN9yZwqqnvc/maxresdefault.jpg'
    },
    {
      name: '怕你知道',
      artist: '庄东茹',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/怕你知道_MQ - 庄东茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/庄东茹《怕你知道》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/5mvInpy15yOywSxMng1cPg==/109951168898869742.jpg?param=130y130'
    },
    {
      name: '轻舟已过万重山',
      artist: '长安三万里',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/轻舟已过万重山 - 长安三万里.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/长安三万里《轻舟已过万重山》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230830/20230830140320873342.jpg'
    },
    {
      name: '我想我不够好',
      artist: '阿妹啊',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我想我不够好 - 阿妹啊.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿妹啊《我想我不够好》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200409/20200409141714931471.jpg'
    },
    {
      name: '被迫出局',
      artist: '绝世小雪琪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/被迫出局 - 绝世小雪琪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/绝世小雪琪《被迫出局》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211229/20211229092329873754.jpg'
    },
    {
      name: '去年的花开',
      artist: '李郝',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/李郝 - 去年的花开.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/去年的花开 - 李郝.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210622/20210622014225845556.jpg'
    },
    {
      name: '最美的伤口',
      artist: 'DJ小鱼儿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最美的伤口 - DJ小鱼儿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/DJ小鱼儿《最美的伤口》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20191229/20191229192013582687.jpg'
    },
    {
      name: '堕',
      artist: '吴岱林',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/堕 - 吴岱林.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/吴岱林《堕》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221020/20221020110644859955.jpg'
    },
    {
      name: '千万个瞬间',
      artist: '庄东茹',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/千万个瞬间_MQ - 庄东茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/庄东茹《千万个瞬间》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/zVA4l4xeMzkjM_DAF-LzMA==/109951169277970635.jpg?param=130y130'
    },
    {
      name: '回忆观影券',
      artist: 'IN-K,王忻辰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/回忆观影券 - IN-K,王忻辰.mp3',
      lrc: '',
      cover:
        'https://p1.music.126.net/K5LLWd9V_sRc2n9Z_Hbgbw==/109951168292512047.jpg?param=130y130'
    },
    {
      name: '记忆闪帧',
      artist: '陪伴着我',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/陪伴着我 - 记忆闪帧.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/记忆闪帧《陪伴着我》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/1iseW63LaTgaocdkMc-WHQ==/109951168317765511.jpg?param=130y130'
    },
    {
      name: '风吹哪页读哪页',
      artist: '徐西',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风吹哪页读哪页 - 徐西.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/徐西《风吹哪页读哪页》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/KFAGupkmGcV9cdFVxFHoUw==/109951169137642005.jpg?param=130y130'
    },
    {
      name: '你会发光',
      artist: '就是南方凯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/你会发光 - 就是南方凯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/就是南方凯《你会发光》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230713/20230713195052817613.jpg'
    },
    {
      name: '梦回私塾',
      artist: '林玉涵',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/梦回私塾_MQ - 林玉涵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/林玉涵《梦回私塾》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230625/20230625095512386063.jpg'
    },
    {
      name: '为你我受冷风吹',
      artist: 'en',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/为你我受冷风吹 - en.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/en《为你我受冷风吹》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/x4NR5IWmzNXwBGBNp_rVVg==/109951168431974003.jpg?param=130y130'
    },
    {
      name: '猎',
      artist: '玥夏',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/猎 - 玥夏.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/玥夏《猎》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230801/20230801203125590514.jpg'
    },
    {
      name: '每当我',
      artist: 'DJ阿智',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/每当我（DJ阿智 Remix） - DJ阿智.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/每当我(DJ版)-李先森.lrc',
      cover:
        'https://p1.music.126.net/I4g1CJPJvCaAUbjRGSwK3Q==/109951169272131918.jpg?param=130y130'
    },
    {
      name: '画神又画鬼',
      artist: '阿禹ayy',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/画神又画鬼 - 阿禹ayy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿禹ayy《画神又画鬼》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/jfWvEsqGc_8yhbqbV8S53g==/109951168100838735.jpg?param=130y130'
    },
    {
      name: '静悄悄',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/静悄悄 - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔小乔《静悄悄》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220408/20220408182810364934.jpg'
    },
    {
      name: '热恋夏季',
      artist: '彭宇昕Chloe',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/热恋夏季 - 彭宇昕Chloe.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/彭宇昕Chloe《热恋夏季》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230808/20230808173740939441.jpg'
    },
    {
      name: '不敌她',
      artist: '指尖笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/不敌她 - 指尖笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/指尖笑《不敌她》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/BsoR4j_6SLCxD2rCGaAiKw==/109951168907819061.jpg?param=130y130'
    },
    {
      name: '彩虹风',
      artist: '陈子晴',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/彩虹风 - 陈子晴.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈子晴《彩虹风》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220629/20220629115059494664.jpg'
    },
    {
      name: '醉千觞',
      artist: '王梓钰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/醉千觞 - 王梓钰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王梓钰《醉千觞》[Mp3_Lrc].lrc',
      cover:
        'https://i.kfs.io/album/global/263332249,0v1/fit/500x500.jpg'
    },
    {
      name: '花开你未来',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花开你未来 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蓝心羽《花开你未来》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230714/20230714160543111240.jpg'
    },
    {
      name: '最好的安排 (释怀版)',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最好的安排 (释怀版)_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/苏星婕《最好的安排（释怀版）》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230813/20230813171622377108.jpg'
    },
    {
      name: '世界别为我担心',
      artist: '尹昔眠',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/世界别为我担心 - 尹昔眠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/尹昔眠《世界别为我担心》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230710/20230710164114432310.jpg'
    },
    {
      name: '落空',
      artist: '白允y',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落空 - 白允y.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/白允y《落空》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/BPPc3j0P9R-B2rvC3Eql-Q==/109951169078533581.jpg?param=130y130'
    },
    {
      name: '花',
      artist: '不知名烟嗓',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花 - 不知名烟嗓.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不知名烟嗓《花》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230217/20230217162252578065.jpg'
    },
    {
      name: '巨蟹座',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/巨蟹座 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《巨蟹座》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220701/20220701192428165777.jpg'
    },
    {
      name: '雾里',
      artist: '温屿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雾里 - 温屿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/温屿《雾里》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/_BP1O9b6ul2HBy-kcPtQRQ==/109951166079790469.jpg?param=130y130'
    },
    {
      name: '鲲鲲进行曲 (0.8x)',
      artist: '沈乐',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/鲲鲲进行曲 (0.8x) - 沈乐.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/鲲鲲进行曲 - 兮雅_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/0eBConsur4ghIhTfNLU3MA==/109951167611318783.jpg?param=130y130'
    },
    {
      name: '飞云之下',
      artist: '韩红,林俊杰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/飞云之下 - 韩红,林俊杰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/韩红&林俊杰《飞云之下》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/YsQrEZ_M6kduwN2zh6Q6kg==/109951163311406661.jpg?param=130y130'
    },
    {
      name: '烟雨行舟',
      artist: '司南',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/烟雨行舟 - 司南.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/司南《烟雨行舟》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/HrOpexiLddSG7wKsPYq-gQ==/109951167056907210.jpg?param=130y130'
    },
    {
      name: '小宇',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/小宇 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蓝心羽《小宇》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/sZRq6Fr7uZCFWkqICtA9Pw==/109951169403477083.jpg?param=130y130'
    },
    {
      name: '那一刻不要忘记我爱你慢摇串烧',
      artist: 'Dj叶仔',
      url: 'https://mp4.djuu.com/c2/16/2024/20e2b96fe4d5bca9.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/68a733b8.jpg'
    },
    {
      name: 'FkHouse重生之我在异乡为异客车载串烧',
      artist: 'DJ小姚',
      url: 'https://mp4.djuu.com/c2/16/2024/833cfc779f9b2804.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/a8d5ad9b.jpg'
    },
    {
      name: '我对你真情流露但最后你去咗边度串烧',
      artist: 'DjHs华少',
      url: 'https://mp4.djuu.com/c2/16/2024/782a38b2fc6bb787.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/12969116.jpg'
    },
    {
      name: '迷幻阿索阿索(FunkyHouse Rmx 2024)',
      artist: 'DJ哈密瓜',
      url: 'https://mp4.djuu.com/c115/45951/118/2024/b3f2397291015533.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/6e222ab3.jpg'
    },
    {
      name: '港台流行女声慢摇串烧',
      artist: 'Dj欢仔',
      url: 'https://mp4.djuu.com/c2/16/2021/6673d8cdb849aef3.m4a',
      cover:
        'https://img.djuu.com/cover/202309/bdaa55.jpg'
    },
    {
      name: '九月第一季热播串烧',
      artist: 'DJ杰仔',
      url: 'https://mp4.djuu.com/c2/16/2021/1dac40b88433a232.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/4c1ba6f3.jpg'
    },
    {
      name: '就是没有女朋友串烧',
      artist: 'Dj白鸡',
      url: 'https://mp4.djuu.com/c2/16/2021/411d406224476777.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/e8dfce6b.jpg'
    },
    {
      name: '龙年最爱中文DJ慢摇串烧',
      artist: '湛江DJ杰仔',
      url: 'https://mp4.djuu.com/c2/16/2024/946b3f92e54eb992.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/6d207f35.jpg'
    },
    {
      name: 'Electro风格情感慢摇串烧',
      artist: 'DJ小政',
      url: 'https://mp4.djuu.com/c2/16/2024/a6e811c369ff1bc0.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/18fb1a8a.jpg'
    },
    {
      name: 'LakHouse超嗨串烧',
      artist: 'DJ傑少',
      url: 'https://mp4.djuu.com/c2/16/2024/050d3c1007d7174c.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/be5bcf60.jpg'
    },
    {
      name: 'Prog风格无名的人DJ串烧',
      artist: 'Dj得滴',
      url: 'https://mp4.djuu.com/c2/16/2024/6101a9909e6638df.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/121e3e13.jpg'
    },
    {
      name: 'Prog爱人如养花你越用心花越漂亮串烧',
      artist: 'Dj阿琦',
      url: 'https://mp4.djuu.com/c2/16/2024/bbd4f0ec466d28b4.m4a',
      cover:
        'https://img.djuu.com/cover/20231001/12969116.jpg'
    },
    {
      name: 'ProgHouse精选流行热播DJ串烧',
      artist: 'DjSix',
      url: 'https://mp4.djuu.com/c2/16/2024/5f5567243e3b1284.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/5b1b119d.jpg'
    },
    {
      name: '冬眠',
      artist: '司南',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/冬眠 - 司南.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/司南《冬眠》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/4KDBaQXnQywQovmqvjx-8Q==/109951164444131697.jpg?param=130y130'
    },
    {
      name: '明知你不再爱我',
      artist: '刘诺儿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/明知你不再爱我_MQ - 刘诺儿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/刘诺儿《明知你不再爱我》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221119/20221119163308450599.jpg'
    },
    {
      name: '间距',
      artist: 'en',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/间距 - en.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/en《间距》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220104/20220104170004144690.jpg'
    },
    {
      name: '罗生门 (0.8x 降调版)',
      artist: '那是残',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/罗生门 (0.8x 降调版) - 那是残.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/罗生门 (0.8x 降调版) - 那是残.lrc',
      cover:
        'https://p2.music.126.net/T07oyJXYO_PCcvdWOPwRUA==/109951167995866956.jpg?param=140y140'
    },
    {
      name: '平凡之路',
      artist: '朴树',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/平凡之路 - 朴树.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/朴树《平凡之路》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/IwEI0tFPh4w9OjY6RM2IJQ==/109951163009071893.jpg?param=130y130'
    },
    {
      name: '阿衣莫',
      artist: '阿吉太组合',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/阿衣莫 - 阿吉太组合.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿吉太组合《阿衣莫》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/T8NN9mGSxJL8GhtCpJjL7w==/109951166336905015.jpg?param=130y130'
    },
    {
      name: '一步之遥',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一步之遥 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《一步之遥》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/uf1yixAOHljTRNHvE7xllw==/109951164837357732.jpg?param=130y130'
    },
    {
      name: '你走过的地方只剩下思念难捱',
      artist: '李思然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/你走过的地方只剩下思念难捱 - 李思然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/李思然《你走过的地方只剩下思念难捱》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211231/20211231150957381386.jpg'
    },
    {
      name: '离人赋',
      artist: '云汐',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/离人赋 - 云汐.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/云汐《离人赋》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/31br_7J70NzB9Ngbi2Um4g==/109951168042206199.jpg?param=130y130'
    },
    {
      name: '灯火通明',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/灯火通明 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《灯火通明》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/d38VlMLVoS6S4yF8DreXzw==/109951168700655655.jpg?param=177y177'
    },
    {
      name: '降临',
      artist: '漆柚',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/降临 - 漆柚.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/漆柚《降临》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220516/20220516175312267912.jpg'
    },
    {
      name: '坏女孩',
      artist: '白允y',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/坏女孩 (cover_ 徐良_小凌) - 白允y.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/白允y《坏女孩》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/gskYwkKO6S8--GEoH144PA==/109951168822160401.jpg?param=177y177'
    },
    {
      name: '你在我眼中绽放',
      artist: '云汐',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/你在我眼中绽放 - 云汐.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/云汐《你在我眼中绽放》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/uyzUfT7CiTdBHPly5Dnu9A==/109951168636710041.jpg?param=130y130'
    },
    {
      name: '萤火',
      artist: '糯米Nomi',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/萤火 - 糯米Nomi.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/糯米Nomi《萤火》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221121/20221121170742107298.jpg'
    },
    {
      name: '一格格',
      artist: 'Ayi阿怡',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一格格 - Ayi阿怡.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Ayi阿怡《一格格》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230417/20230417113118651691.jpg'
    },
    {
      name: '太阳',
      artist: '曲肖冰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/曲肖冰 - 太阳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/曲肖冰《太阳》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/4CnWmlyFCBuEXYXH58Nxdw==/109951164384046724.jpg?param=130y130'
    },
    {
      name: '抽离',
      artist: '徐良,刘丹萌',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/抽离 - 徐良,刘丹萌.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/徐良&刘丹萌《抽离》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/ZbvgR59SPWDOi4vOETC0GA==/109951164006906046.jpg?param=130y130'
    },
    {
      name: '给你一瓶魔法药水',
      artist: '告五人',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/给你一瓶魔法药水 - 告五人.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/告五人《给你一瓶魔法药水》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/W2mKDaDQegJg0ic0GOd2KQ==/109951169169063268.jpg?param=130y130'
    },
    {
      name: '遗憾也值得 ',
      artist: 'cici',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/遗憾也值得 - cici_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/cici_《遗憾也值得》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230310/20230310170133977639.jpg'
    },
    {
      name: '负重一万斤长大',
      artist: '太一',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/负重一万斤长大 - 太一.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/太一《负重一万斤长大》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/YVdUq-VX1TJW_8u3EKAs5g==/109951164545953099.jpg?param=130y130'
    },
    {
      name: '在你的身边 ',
      artist: '盛哲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/在你的身边 - 盛哲.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/盛哲《在你的身边》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210630/20210630181811872340.jpg'
    },
    {
      name: '花海 ',
      artist: '周杰伦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花海 - 周杰伦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/周杰伦《花海》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/HBanuZpt8SD2kf15AFa6Og==/109951163200234839.jpg?param=130y130'
    },
    {
      name: '飞 ',
      artist: '赵今麦,郭俊辰,姜冠南,韩沛颖,ANU',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/飞 - 赵今麦,郭俊辰,姜冠南,韩沛颖,ANU.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/飞-赵今麦,郭俊辰,姜冠南,韩沛颖,ANU.lrc',
      cover:
        'https://p1.music.126.net/3-SYutfygNBo34KiEbn_cg==/109951167720914897.jpg?param=130y130'
    },
    {
      name: '唯一 ',
      artist: '告五人',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/唯一 - 告五人.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/告五人《唯一》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/5URIeP6GjMFg_hKhGloNTA==/109951165585701063.jpg?param=130y130'
    },
    {
      name: '笑纳 ',
      artist: '花僮',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/笑纳 - 花僮.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/花僮《笑纳》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/sEuuyH3gUeZY7i_KNF4TIQ==/109951168237046548.jpg?param=130y130'
    },
    {
      name: '恶性循环 ',
      artist: '尚芸菲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/恶性循环 - 尚芸菲.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/尚芸菲《恶性循环》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220628/20220628164842238359.jpg'
    },
    {
      name: '奔向你 ',
      artist: '周深',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/奔向你 - 周深.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/周深《奔向你》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/ln_AXTfRu3CMKrW33a-HUA==/109951168008349681.jpg?param=130y130'
    },
    {
      name: '空城  ',
      artist: '杨坤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/空城 - 杨坤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/杨坤《空城》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200620/20200620061426992581.jpg'
    },
    {
      name: '选择失忆 ',
      artist: '季彦霖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/选择失忆 - 季彦霖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/季彦霖《选择失忆》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20190327/20190327190503755091.jpg'
    },
    {
      name: '无人之岛',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/无人之岛 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《无人之岛》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/mIUtHBPTJ52H78_FhHzcWg==/19188676928210304.jpg?param=130y130'
    },
    {
      name: '带我去找夜生活',
      artist: '告五人',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/带我去找夜生活 - 告五人.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/告五人《带我去找夜生活》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/lmCtt6-3fOPSdA1uwGCY5Q==/109951164567402626.jpg?param=130y130'
    },
    {
      name: '盛夏的果实 ',
      artist: '莫文蔚',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/莫文蔚 - 盛夏的果实.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/莫文蔚《盛夏的果实》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/XkNW0EH1uHyfLoy0lT-Glw==/109951163447812619.jpg?param=130y130'
    },
    {
      name: '青衣',
      artist: '琪大妈',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/青衣 (新版)_MQ - 琪大妈.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/琪大妈《青衣》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220424/20220424142114848540.jpg'
    },
    {
      name: '你的酒馆对我打了烊 ',
      artist: '陈雪凝',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/你的酒馆对我打了烊 - 陈雪凝.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈雪凝《你的酒馆对我打了烊》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/LiRR__0pJHSivqBHZzbMUw==/109951163816225567.jpg?param=130y130'
    },
    {
      name: '我的名字 ',
      artist: '焦迈奇',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我的名字 - 焦迈奇.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/焦迈奇《我的名字》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/Ppzb5LV4l9R-yki1BQQB0A==/109951166267072680.jpg?param=130y130'
    },
    {
      name: '寂寞烟火 ',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/寂寞烟火 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蓝心羽《寂寞烟火》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/i9tCzr-l5Vyl8d62pfMjsQ==/109951169385759820.jpg?param=130y130'
    },
    {
      name: '雾里 ',
      artist: '姚六一',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雾里 - 姚六一.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/姚六一《雾里》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221205/20221205181129929471.jpg'
    },
    {
      name: 'New Boy',
      artist: '房东的猫',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/New Boy  - 房东的猫.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/房东的猫《New Boy》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/KkrcSwKbRsd8GuaOHILlxA==/109951166077317301.jpg?param=130y130'
    },
    {
      name: '深渊之下 ',
      artist: '谜兔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/深渊之下 - 谜兔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/谜兔《深渊之下》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220309/20220309173736338268.jpg'
    },
    {
      name: '海市蜃楼 ',
      artist: '三叔说',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/海市蜃楼 - 三叔说.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/海市蜃楼-三叔说.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230113/20230113233651466000.jpg'
    },
    {
      name: '百年孤寂 ',
      artist: '魏晗（懋懋）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/百年孤寂 - 魏晗（懋懋）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/魏晗（懋懋）《百年孤寂》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20241031/20241031101344322793.jpg'
    },
    {
      name: '天外来物 ',
      artist: '薛之谦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/天外来物 - 薛之谦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/薛之谦《天外来物》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20201231/20201231105006379600.jpg'
    },
    {
      name: '遥远的你 ',
      artist: '221小伙伴',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/遥远的你 - 221小伙伴.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/221小伙伴《遥远的你》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210114/20210114203131171628.jpg'
    },
    {
      name: '李如意 - DJ明天会更好',
      artist: 'Dj桃子阿',
      url: 'https://mp4.djuu.com/c4/26/2022/fb617d40cbda523e.m4a',
      cover:
        'https://img.djuu.com/cover/202209/4b7313.jpg'
    },
    {
      name: '朱雅 - DJ前度',
      artist: '南宁Dj杰仔',
      url: 'https://mp4.djuu.com/c4/22/2019/158a3b862c4cc58a.m4a',
      cover:
        'https://img.djuu.com/cover/201806/a65216.jpg'
    },
    {
      name: '陈一发儿 - DJ童话镇',
      artist: '贺州Dj小林',
      url: 'https://mp4.djuu.com/c4/26/2019/65de0fb3aa3265d9.m4a',
      cover:
        'https://img.djuu.com/cover/201806/75f079.jpg'
    },
    {
      name: 'DJ鞠文娴 - BINGBIAN病变',
      artist: 'Dj小晨',
      url: 'https://mp4.djuu.com/c4/24/2018/eb16d5270ed9bc2c.m4a',
      cover:
        'https://img.djuu.com/cover/202309/873fe7.jpg'
    },
    {
      name: 'Sing倩儿 - DJ体面',
      artist: 'Dj十三',
      url: 'https://mp4.djuu.com/c4/25/2018/518b533c4486f502.m4a',
      cover:
        'https://img.djuu.com/cover/2023929/1ed7980b.jpg'
    },
    {
      name: 'Simyee陈芯怡 - DJ青花瓷',
      artist: '岑溪DJ辉仔',
      url: 'https://mp4.djuu.com/c115/41838/117/2024/9c295b685669459e.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/18ef1c35.jpg'
    },
    {
      name: '邓紫棋 - DJ我的秘密',
      artist: 'Dj小罗',
      url: 'https://mp4.djuu.com/c4/26/2023/8414bd05d5b14f09.m4a',
      cover:
        'https://img.djuu.com/cover/201806/a965f7.jpg'
    },
    {
      name: '邓紫棋 - DJ来自天堂的魔鬼',
      artist: '网络歌手',
      url: 'https://mp4.djuu.com/c4/107/2022/fc2ef1eb77f3cbe8.m4a',
      cover:
        'https://p1.music.126.net/kVwk6b8Qdya8oDyGDcyAVA==/1364493930777368.jpg?param=130y130'
    },
    {
      name: '曲肖冰 - DJ谁',
      artist: 'Dj光头',
      url: 'https://mp4.djuu.com/c4/26/2024/d09d0c752b54c40c.m4a',
      cover:
        'https://img.djuu.com/cover/202202/8e48bb.jpg'
    },
    {
      name: '指尖笑 - DJ不问别离',
      artist: 'Dj可仔',
      url: 'https://mp4.djuu.com/c4/26/2024/6af3faaa53f6382a.m4a',
      cover:
        'https://p2.music.126.net/CngdcKlEeF9AwfTcKbQFpQ==/109951168592751340.jpg?param=130y130'
    },
    {
      name: '收购美好',
      artist: '黄文文',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/收购美好 - 黄文文.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/黄文文《收购美好》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200225/20200225175718988729.jpg'
    },
    {
      name: '无归',
      artist: '咻咻满',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/无归 - 咻咻满.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/咻咻满《无归》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/7YeE_wdkWlyROCzQ-HuMPA==/109951168600979306.jpg?param=130y130'
    },
    {
      name: '半阙',
      artist: '灼夭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/半阙 - 灼夭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/灼夭&小田音乐社《半阙》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/ZtGenDZY8CT4gsQ8Sn_l2Q==/109951167949078988.jpg?param=177y177'
    },
    {
      name: '夏天和你拥入怀中',
      artist: 'LBI利比（时柏尘）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/夏天和你拥入怀中_MQ - LBI利比（时柏尘）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/LBI利比（时柏尘）《夏天和你拥入怀中》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/h8VEJA2lZKepM12-rFdnhQ==/109951168663851535.jpg?param=130y130'
    },
    {
      name: '最后一页 (清新版)',
      artist: 'Ssweetxin',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最后一页 - Ssweetxin_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Ssweetxin_《最后一页》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/XvVUZQTCxmhjNOcfEnJYew==/109951163610134059.jpg?param=130y130'
    },
    {
      name: '此刻的风',
      artist: '承桓',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/此刻的风 - 承桓.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/承桓《此刻的风》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/daqrD4GyZ5oxsOpf0rjT-A==/109951169482646177.jpg?param=130y130'
    },
    {
      name: '携风予你',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/携风予你 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《携风予你》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/GEKfzvI0Op5T_8Z-jm0bqA==/109951167523680350.jpg?param=130y130'
    },
    {
      name: '满江红遍',
      artist: '玄觞',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/满江红遍 - 玄觞.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/玄觞《满江红遍》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230331/20230331160157822368.jpg'
    },
    {
      name: '等一个对的人',
      artist: '范茹',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/等一个对的人 - 范茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/范茹《等一个对的人》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230608/20230608154436146620.jpg'
    },
    {
      name: '在等',
      artist: '陈亦洺、罗文Rown',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/在等_MQ - 陈亦洺、罗文Rown.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈亦洺&罗文Rown《在等》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230707/20230707145719742668.jpg'
    },
    {
      name: 'DJ蔡健雅 - Letting Go',
      artist: 'DjRn',
      url: 'https://mp4.djuu.com/c4/26/2024/49318c0350787e82.m4a',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220408/20220408121218498989.jpg'
    },
    {
      name: '陈雪凝 - DJ绿色',
      artist: 'Dj六少飞',
      url: 'https://mp4.djuu.com/c4/26/2024/7b3609110d9fef62.m4a',
      cover:
        'https://p2.music.126.net/R4ZP3AJ9xV0vvw8LX7AbMA==/109951163860425334.jpg?param=130y130'
    },
    {
      name: '言瑾羽 - DJ未必',
      artist: 'Dj六少飞',
      url: 'https://mp4.djuu.com/c4/26/2024/5c15f6c41c2f9081.m4a',
      cover:
        'https://p1.music.126.net/A65wzMHAqccx3IU8GYKyEA==/109951169244595192.jpg?param=130y130'
    },
    {
      name: '孙语赛vs萧全 - DJ不仅仅是喜欢',
      artist: 'DjYg',
      url: 'https://mp4.djuu.com/c4/21/2018/de87f6b04f6fe3a2.m4a',
      cover:
        'https://p2.music.126.net/SLKWLEray70Ki8VlHo1Vig==/109951163974008645.jpg?param=130y130'
    },
    {
      name: '就是南方凯 - DJ离别开出花',
      artist: 'Dj桃子啊',
      url: 'https://mp4.djuu.com/c4/26/2024/51e4254c2bc70fc2.m4a',
      cover:
        'https://p1.music.126.net/MgrERz_auwIi5Km64TxwjQ==/109951169154658797.jpg?param=177y177'
    },
    {
      name: '赵方婧 - DJ尽头',
      artist: 'DJK2',
      url: 'https://mp4.djuu.com/c115/16502/117/2023/2b032af31bf60382.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20200708/20200708162704632520.jpg'
    },
    {
      name: 'Dehors',
      artist: 'Jordann',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Dehors - Jordann.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Dehors-JORDANN.lrc',
      cover:
        'https://p1.music.126.net/7IM_iRaCe2Pe9TU7PMTdqQ==/109951169635570073.jpg?param=130y130'
    },
    {
      name: '下雨天',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/下雨天 - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔小乔《下雨天》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230705/20230705121501881683.jpg'
    },
    {
      name: '西游G',
      artist: '阿禹ayy',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/西游G - 阿禹ayy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿禹ayy《西游G》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/A14DLjwCLQbhJb-DYicTrg==/109951169003743919.jpg?param=130y130'
    },
    {
      name: '我们俩',
      artist: '不知名烟嗓',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我们俩 - 不知名烟嗓.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不知名烟嗓《我们俩》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230616/20230616161424532843.jpg'
    },
    {
      name: '静悄悄',
      artist: '不够',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/静悄悄 - 不够.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不够《静悄悄》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/uvEdB1dIkdJGCQrdM2i8Ag==/109951168221512786.jpg?param=130y130'
    },
    {
      name: '落单雪人',
      artist: '刘增瞳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落单雪人 - 刘增瞳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/刘增瞳《落单雪人》[Mp3_Lrc].lrc',
      cover:
        'https://cdnmusic.migu.cn/picture/2023/0913/1921/AMd8e33f05cf92913cdf2985ad947f1341.jpg'
    },
    {
      name: '清晨有粥 黄昏有酒',
      artist: 'Uu (刘梦妤)',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/清晨有粥 黄昏有酒 - Uu (刘梦妤).mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/清晨有粥 黄昏有酒-Uu.lrc',
      cover:
        'https://p1.music.126.net/hyJODphJ3949xK9kgMttcA==/109951168565359609.jpg?param=177y177'
    },
    {
      name: '佳衣',
      artist: '平生不晚',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/佳衣 - 平生不晚.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/平生不晚《佳衣》[Mp3_Lrc].lrc',
      cover:
        'https://img.1ting.com/images/special/466/s300_8f556ad283dc84516a39813b0ef4f28a.jpg'
    },
    {
      name: '真的不快乐',
      artist: '李飘飘',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/真的不快乐 - 李飘飘.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/李飘飘《真的不快乐》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/Cd8iZbgv7LYFWKv4MzJZ4w==/109951168015255045.jpg?param=130y130'
    },
    {
      name: '江语晨 - DJ最后一页',
      artist: 'Dj小邓',
      url: 'https://mp4.djuu.com/c4/26/2024/6ea6deb8c3e18845.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20191001/20191001174509470283.jpg'
    },
    {
      name: '阿肆 - DJ热爱105度的你',
      artist: 'Dj小邓',
      url: 'https://mp4.djuu.com/c4/26/2024/2abc457865f9c955.m4a',
      cover:
        'https://p1.music.126.net/6Herq6VjqEM2wJYiML3y4A==/109951166098679116.jpg?param=130y130'
    },
    {
      name: '单色凌 - DJ我想我不够好',
      artist: 'DjAn',
      url: 'https://mp4.djuu.com/c4/26/2024/0cfe5e094903638c.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20200620/20200620080504229934.jpg'
    },
    {
      name: '告五人 - DJ带我去找夜生活',
      artist: 'Dj泡面',
      url: 'https://mp4.djuu.com/c4/26/2024/4760bb54e92b7c10.m4a',
      cover:
        'https://p1.music.126.net/lmCtt6-3fOPSdA1uwGCY5Q==/109951164567402626.jpg?param=130y130'
    },
    {
      name: '锦零 - DJ豆花之歌',
      artist: 'Dj小雨',
      url: 'https://mp4.djuu.com/c4/26/2024/2069e614b1b4dccf.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20220222/20220222060106813121.jpg'
    },
    {
      name: '独处花园',
      artist: '司南',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/独处花园 - 司南.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/司南《独处花园》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/GwGp8uvwTLSDE8UtEg2R7Q==/109951168829911745.jpg?param=130y130'
    },
    {
      name: '缺',
      artist: '镜予歌、亡海Aries',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/缺_MQ - 镜予歌、亡海Aries.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/缺 - 镜予歌&亡海Aries_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/CLKm7H15Dr1z_4QaX43ILA==/109951167906060738.jpg?param=130y130'
    },
    {
      name: '相思酿成酒',
      artist: '残雪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/相思酿成酒 - 残雪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/残雪《相思酿成酒》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230315/20230315141140413714.jpg'
    },
    {
      name: '夏日情书',
      artist: '葛雨晴',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/夏日情书 - 葛雨晴.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/葛雨晴《夏日情书》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220817/20220817172410558153.jpg'
    },
    {
      name: '辞归',
      artist: '指尖笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/辞归 - 指尖笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/指尖笑《辞归》[Mp3_Lrc].lrc',
      cover:
        'https://lineimg.omusic.com.tw/img/album/5974483.jpg?v=20220713195301'
    },
    {
      name: '变脸',
      artist: '阿禹ayy',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/变脸 - 阿禹ayy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿禹ayy《变脸》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230407/20230407110347789470.jpg'
    },
    {
      name: '催熟',
      artist: '欧阳朵',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/催熟 - 欧阳朵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/欧阳朵《催熟》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/3R5P9-kPkt0vJJszEw0INA==/109951168602510691.jpg?param=130y130'
    },
    {
      name: '阿比拿铁',
      artist: '三无Marblue',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/阿比拿铁 - 三无Marblue.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/三无Marblue《阿比拿铁》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/p7gcA3P_iGfq-yaG8NAHyQ==/109951167420757780.jpg?param=130y130'
    },
    {
      name: '我的眼泪你的战利品',
      artist: '于冬然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我的眼泪你的战利品 - 于冬然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/于冬然《我的眼泪你的战利品》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20240822/20240822161714248710.jpg'
    },
    {
      name: '这个冬天一起看雪',
      artist: 'K.D',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/这个冬天一起看雪_MQ - K.D.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/K.D《这个冬天一起看雪》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221006/20221006163919744246.jpg'
    },
    {
      name: '梦涵 - DJ爱的故事上集',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/cbdc7e8ac669aad0.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20180119/20180119195945318456.jpg'
    },
    {
      name: '小5 - DJ说一句我不走了',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/f793496b26ea97b2.m4a',
      cover:
        'https://imgessl.kugou.com/stdmusic/20150718/20150718224349751478.jpg'
    },
    {
      name: '温奕心 - DJ一路生花',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2021/013824108c974246.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20240807/20240807180417321818.jpg'
    },
    {
      name: '陈之 - DJ月亮惹的祸',
      artist: 'Dj阿福',
      url: 'https://mp4.djuu.com/c4/26/2024/d41c7d265cad3cbd.m4a',
      cover:
        'https://p1.music.126.net/4LuKCZHLwmXLBeTY_t0Qjw==/109951164781178865.jpg?param=130y130'
    },
    {
      name: 'DJ 美丽的神话',
      artist: 'Dj伦大人',
      url: 'https://mp4.djuu.com/c4/26/2023/83c3d719e0596aa1.m4a',
      cover:
        'https://img.djuu.com/cover/201902/bb2057.jpg'
    },
    {
      name: '田馥甄 - DJ你就不要想起我',
      artist: 'Dj尤宏',
      url: 'https://mp4.djuu.com/c4/26/2024/0594b41f5be3e438.m4a',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20230818/20230818170841782.jpg'
    },
    {
      name: '风驶过的声音是 (说唱版)',
      artist: '泽阳、二两',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风驶过的声音是 - 泽阳、二两.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风驶过的声音是 - 泽阳&二两_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/gfLR-3TEncp1cOog8oUaFA==/109951168567036973.jpg?param=130y130'
    },
    {
      name: '以自己为中心才是lifestyle',
      artist: '歪歪超',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/以自己为中心才是lifestyle - 歪歪超.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/歪歪超《以自己为中心才是lifestyle》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221129/20221129172001118576.jpg'
    },
    {
      name: '黑夜',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/黑夜_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/苏星婕《黑夜》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/38lox6LxSFlHnUD2yU-rEg==/109951167378315186.jpg?param=130y130'
    },
    {
      name: '童话部落',
      artist: '南栖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/童话部落 - 南栖,小田音乐社.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/南栖&小田音乐社《童话部落》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/Djtq-NqZBRJK-GJ2KLmxmg==/109951168050802828.jpg?param=177y177'
    },
    {
      name: 'Black Magic',
      artist: 'Jonasu',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Black Magic - Jonasu.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Jonasu《Black Magic》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/KFS9Z18U07UQKuhXWaFZOg==/109951165549454434.jpg?param=130y130'
    },
    {
      name: '月黑风高夜',
      artist: '阿禹ayy ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/月黑风高夜 - 阿禹ayy.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿禹ayy《月黑风高夜》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221031/20221031152007389886.jpg'
    },
    {
      name: '一直很安静 (拾忆版)',
      artist: 'cici',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一直很安静 - cici_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/cici_《一直很安静》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230704/20230704135827978072.jpg'
    },
    {
      name: '爱人错过 (治愈版)',
      artist: 'cici',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/爱人错过 - cici_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/cici_《爱人错过》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230808/20230808175944366359.jpg'
    },
    {
      name: '不问别离',
      artist: '指尖笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/不问别离 - 指尖笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不问别离 - 指尖笑.lrc',
      cover:
        'https://p1.music.126.net/CngdcKlEeF9AwfTcKbQFpQ==/109951168592751340.jpg?param=130y130'
    },
    {
      name: '躲进星海',
      artist: '袁小葳、阿辰（阎辰）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/躲进星海 - 袁小葳,阿辰（阎辰）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/袁小葳&阿辰（阎辰）《躲进星海》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230609/20230609112710163029.jpg'
    },
    {
      name: '张靓颖 - DJ画心',
      artist: 'DjRbin',
      url: 'https://mp4.djuu.com/c4/26/2023/ea7ee982cb30f697.m4a',
      cover:
        'https://p1.music.126.net/9YeRLbaJmAc3FiHRxJXxzw==/109951165641456869.jpg?param=130y130'
    },
    {
      name: '王优秀 - DJ想某人',
      artist: 'Dj小豪',
      url: 'https://mp4.djuu.com/c4/26/2023/a856bd88c7c4fc65.m4a',
      cover:
        'https://img.djuu.com/cover/202206/8a69db.jpg'
    },
    {
      name: '林俊杰 - DJ起风了',
      artist: 'Dj阿帆',
      url: 'https://mp4.djuu.com/c4/26/2023/086d5f4ad5fb23ce.m4a',
      cover:
        'https://img.djuu.com/cover/201806/7b46fb.jpg'
    },
    {
      name: '曲肖冰 - DJ等一分钟',
      artist: 'Dj178',
      url: 'https://mp4.djuu.com/c4/26/2023/f26b0e2711cd7a7d.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20220330/20220330010801944734.jpg'
    },
    {
      name: '曲肖冰 - DJ天亮以前说再见',
      artist: 'Dj史迪奇',
      url: 'https://mp4.djuu.com/c4/26/2024/484a21f11787d5d1.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20210113/20210113204929393980.jpg'
    },
    {
      name: '闻人听書 - DJ虞兮叹',
      artist: 'Dj史迪奇',
      url: 'https://mp4.djuu.com/c4/26/2024/ffcee40c6e718a10.m4a',
      cover:
        'https://p1.music.126.net/6gdwWjPXUkyTx4CuuSxkIg==/109951165319864977.jpg?param=130y130'
    },
    {
      name: '蒋蒋 vs 曲肖冰 - 当真',
      artist: 'DjRichz',
      url: 'https://mp4.djuu.com/c4/26/2023/cb2fa8f58a98f360.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20221031/20221031192709957415.jpg'
    },
    {
      name: '最后一页 (治愈版）',
      artist: 'cici',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最后一页 - cici_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/最后一页(治愈版)-cici_.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230816/20230816152729423845.jpg'
    },
    {
      name: '相同的情话',
      artist: '逸霄、甜芋儿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/相同的情话 - 逸霄、甜芋儿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/逸霄&甜芋儿《相同的情话》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221223/20221223144826174870.jpg'
    },
    {
      name: '我是卷王',
      artist: '庄东茹',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我是卷王_MQ - 庄东茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/庄东茹《我是卷王》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/Q_Laam-YJvOM-UlalJ9wNw==/109951170126710181.jpg?param=130y130'
    },
    {
      name: 'That Girl (加速版)',
      artist: '子榕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/That Girl - 子榕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/That Girl (加速版) - 子榕.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230713/20230713173218765501.jpg'
    },
    {
      name: '我下班了蟹老板',
      artist: '歪歪超',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我下班了蟹老板 - 歪歪超.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/歪歪超《我下班了蟹老板》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220901/20220901180649997946.jpg'
    },
    {
      name: '偷星星的月亮 (暖冬版)',
      artist: '张红山KaKa、蓝意',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/偷星星的月亮 - 张红山KaKa,蓝意.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/邓阿九&孙才垣《偷星星的月亮》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221220/20221220161047105853.jpg'
    },
    {
      name: '像雨吹起了风',
      artist: 'Liko、庄淇文29',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/像雨吹起了风 - Liko,庄淇文29.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Liko&庄淇玟29《像雨吹起了风》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220302/20220302152633301911.jpg'
    },
    {
      name: '枫叶与冬雪',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/枫叶与冬雪_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/苏星婕《枫叶与冬雪》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/9HvxZ-N_9nx7t-QQqiLo-g==/109951168181703928.jpg?param=130y130'
    },
    {
      name: '郎中',
      artist: '蛋黄',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/郎中 - 蛋黄.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蛋黄《郎中》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/rlxpKT5qPXN-1db4AszNrw==/109951168116726366.jpg?param=130y130'
    },
    {
      name: '见面就告白吧',
      artist: '庄东茹',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/见面就告白吧_MQ - 庄东茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/庄东茹《见面就告白吧》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/a9hjM3Vx02EPq5GOHIabOw==/109951168607023906.jpg?param=130y130'
    },
    {
      name: '邵雨涵 - DJ耍太极',
      artist: 'Dj小M',
      url: 'https://mp4.djuu.com/c4/26/2023/c9d6d2285d49ad2c.m4a',
      cover:
        'https://img.djuu.com/cover/202206/bef0d7.jpg'
    },
    {
      name: '苏星婕 - DJ听悲伤的情歌',
      artist: 'Dj小M',
      url: 'https://mp4.djuu.com/c4/26/2023/874fc80916a326e7.m4a',
      cover:
        'https://img.djuu.com/cover/202206/7ca51a.jpg'
    },
    {
      name: '阿YueYue - DJ云与海',
      artist: 'Dj锦先森',
      url: 'https://mp4.djuu.com/c4/26/2023/711373860f7cc4a8.m4a',
      cover:
        'https://p1.music.126.net/JbuSOzWmpQEVRl6JWJF7-w==/109951165525173781.jpg?param=130y130'
    },
    {
      vsvsname: '阿肆vs郭采洁-DJ世界上的另一个我',
      artist: 'K6',
      url: 'https://mp4.djuu.com/c4/26/2023/30d860c639e542c8.m4a',
      cover:
        'https://p1.music.126.net/0Nt48fegVYo7SYo1-wi8tQ==/109951166589063810.jpg?param=130y130'
    },
    {
      name: '许佳慧 - DJ预谋',
      artist: 'Dj阿欧',
      url: 'https://mp4.djuu.com/c4/26/2023/16e9db11d3a67826.m4a',
      cover:
        'https://imge.kugou.com/stdmusic/20230818/20230818175343273626.jpg'
    },
    {
      name: 'Illusionary Daytime',
      artist: 'xxxCr3',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Illusionary Daytime - xxxCr3.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Illusionary Daytime - MAMUSUONA_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/8xNVCemkSNQptEyNw1PHKg==/8914840278033758.jpg?param=130y130'
    },
    {
      name: 'Wish My Mind Would Shut Up',
      artist: 'LalaBay',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Wish My Mind Would Shut Up - LalaBay.mp3',
      lrc: '',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230613/20230613182020480318.jpg'
    },
    {
      name: '让风告诉你 (说唱版)',
      artist: '黑色绮子',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/让风告诉你 - 黑色绮子.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/黑色绮子《让风告诉你》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230613/20230613182020480318.jpg'
    },
    {
      name: '风驶过的声音是',
      artist: 'Sorry 17',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风驶过的声音是 - Sorry 17.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Sorry 17《风驶过的声音是》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/gfLR-3TEncp1cOog8oUaFA==/109951168567036973.jpg?param=130y130'
    },
    {
      name: '爱自己的100种方式',
      artist: '歪歪超',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/爱自己的100种方式 - 歪歪超.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/歪歪超《爱自己的100种方式》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230616/20230616143224988337.jpg'
    },
    {
      name: '笑柄',
      artist: '陈小满',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/笑柄 - 陈小满.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/陈小满《笑柄》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210707/20210707104035478312.jpg'
    },
    {
      name: '醒不来的梦',
      artist: '回小仙',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/醒不来的梦 - 回小仙.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/回小仙《醒不来的梦》[Mp3_Lrc]2.lrc',
      cover:
        'https://p2.music.126.net/cZZ2ZHVV9M3tNoLPiiP7cA==/109951165608808814.jpg?param=177y177'
    },
    {
      name: '我在原地等你',
      artist: '耗子',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我在原地等你 - 耗子.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/耗子《我在原地等你》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210713/20210713160042371167.jpg'
    },
    {
      name: '凉城 ',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/凉城 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《凉城》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221114/20221114144141101968.jpg'
    },
    {
      name: '沦陷',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/沦陷 - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔小乔《沦陷》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210515/20210515123058583875.jpg'
    },
    {
      name: '落差 ',
      artist: 'IN-K,王忻辰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落差 - IN-K,王忻辰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/IN-K&王忻辰《落差》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/TsRQNSbWVeHvvzZpV85--w==/109951168198900111.jpg?param=130y130'
    },
    {
      name: '落海',
      artist: '任然',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落海 - 任然.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/任然《落海》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210429/20210429160149720540.jpg'
    },
    {
      name: '我很好 (吉他版)',
      artist: '刘大壮',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我很好 (吉他版)_MQ - 刘大壮.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/我很好 (吉他版) - 刘大壮.lrc',
      cover:
        'https://p1.music.126.net/qGIzwzf05taTVfk9PSnSiw==/109951165424768428.jpg?param=130y130'
    },
    {
      name: '父亲写的散文诗',
      artist: '许飞',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/父亲写的散文诗 - 许飞.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/许飞《父亲写的散文诗》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/O2QqVDwgnGVfO8GECh3U5Q==/109951168244970054.jpg?param=130y130'
    },
    {
      name: '雪中散火',
      artist: '玄觞',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雪中散火_MQ - 玄觞.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/玄觞《雪中散火》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220301/20220301111251232014.jpg'
    },
    {
      name: '输入法打可爱按第五 ',
      artist: 'INTO1-米卡',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/输入法打可爱按第五 - INTO1-米卡.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/米卡&高卿尘Nine&吴宇恒&薛八一&张星特&曾涵江Cup《输入法打可爱按第五》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220909/20220909213011442190.jpg'
    },
    {
      name: '画离弦 ',
      artist: '海伦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/画离弦 - 海伦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/海伦《画离弦》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210827/20210827105633980629.jpg'
    },
    {
      name: '游京',
      artist: 'MC海伦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/游京 - 海伦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/海伦《游京》[Mp3_Lrc]2.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20201010/20201010172408362673.jpg'
    },
    {
      name: '带你去旅行',
      artist: '校长（张驰）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/带你去旅行 - 校长（张驰）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/校长（张驰）《带你去旅行》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20200620/20200620055910825151.jpg'
    },
    {
      name: 'But You ',
      artist: 'LaiLai',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/But You - LaiLai.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/LaiLai《But You》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220722/20220722155318690055.jpg'
    },
    {
      name: '下坠Falling ',
      artist: 'Corki',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/下坠Falling - Corki.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Corki刘宗鑫《下坠Falling》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/TyQNalBMYwsCOl5sq_NBTA==/109951164573566845.jpg?param=130y130'
    },
    {
      name: '自娱自乐',
      artist: '金志文',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/自娱自乐 - 金志文.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/金志文《自娱自乐》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210827/20210827142426238510.jpg'
    },
    {
      name: '劫',
      artist: '王雨桐',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/劫 - 王雨桐.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/王雨桐《劫》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221205/20221205164124766929.jpg'
    },
    {
      name: '落差 (女声版)',
      artist: '梨兮',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落差 (女声版) - 梨兮.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/梨兮 - 落差 (女声版) (51091868).lrc',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/150/20231101/20231101163410710.jpg'
    },
    {
      name: '画皮 ',
      artist: '小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/画皮 - 小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小蓝背心《画皮》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230106/20230106162109241927.jpg'
    },
    {
      name: '等着我回来 ',
      artist: '戴羽彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/等着我回来 - 戴羽彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/戴羽彤《等着我回来》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221209/20221209164622542468.jpg'
    },
    {
      name: '浪人琵琶',
      artist: '胡66,单色凌',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/浪人琵琶 - 胡66,单色凌.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/胡66&单色凌《浪人琵琶》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210114/20210114121910121493.jpg'
    },
    {
      name: '年少小欢喜',
      artist: '大柯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/年少小欢喜_MQ - 大柯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/大柯《年少小欢喜》[Mp3_Lrc].lrc',
      cover:
        'https://is1-ssl.mzstatic.com/image/thumb/Music116/v4/ef/00/be/ef00bef8-ec01-87de-99a0-9f7d6074b3dc/6941808077358.jpg/316x316bb.webp'
    },
    {
      name: '火红的萨日朗',
      artist: '要不要买菜',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/火红的萨日朗 - 要不要买菜.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/徐泽（要不要买菜）《火红的萨日朗》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20191210/20191210144311326366.jpg'
    },
    {
      name: '那一刻心动',
      artist: '蔡鹤峰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/那一刻心动 - 蔡鹤峰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/蔡鹤峰《那一刻心动》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210606/20210606205504906738.jpg'
    },
    {
      name: '伪善者',
      artist: '金渔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/伪善者 - 金渔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/金渔《伪善者》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/OK33U9yW15NQjWYRa9DV_Q==/109951167165464079.jpg?param=130y130'
    },
    {
      name: '伯虎说 ',
      artist: '伯爵Johnny、唐伯虎Annie',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/伯虎说 - 伯爵Johnny、唐伯虎Annie.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/伯爵Johnny&唐伯虎Annie《伯虎说 (feat.唐伯虎Annie)》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/0TVcp5_x6GuBnBa9rJhFvQ==/109951165500305096.jpg?param=177y177'
    },
    {
      name: '胆怯 ',
      artist: '占二曦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/胆怯 - 占二曦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/占二曦《胆怯》[Mp3_Lrc]2.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211216/20211216170342482085.jpg'
    },
    {
      name: '戏影',
      artist: '彭十六',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/戏影 - 彭十六.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/彭十六《戏影》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20240823/20240823173802688287.jpg'
    },
    {
      name: '惊雷（抒情版）',
      artist: '皮卡丘多多',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/惊雷（抒情版） - 皮卡丘多多.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/皮卡丘多多《惊雷》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/p1BFEG629R1dOjepX1hdEA==/109951164846963078.jpg?param=130y130'
    },
    {
      name: '坏女孩',
      artist: '林怡婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/坏女孩 - 林怡婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/林怡婕《坏女孩》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211224/20211224135500853057.jpg'
    },
    {
      name: '灰色光鲜 ',
      artist: '丁芙妮',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/灰色光鲜 - 丁芙妮.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/丁芙妮《灰色光鲜》[Mp3_Lrc].lrc',
      cover:
        'https://cdnmusic.migu.cn/picture/2023/0516/1756/AM5cb649040a90ff64c04531c2eb76854a.jpg'
    },
    {
      name: '지나갈 테니 (Been Through)',
      artist: 'EXO',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/지나갈 테니 (Been Through) - EXO.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/EXO《지나갈 테니 (Been Through)》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/jVOiSOzmNgZ0Y-nexDlNxg==/109951167724454186.jpg?param=130y130'
    },
    {
      name: '可能',
      artist: '程响',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/可能 - 程响.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/程响《可能》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/WEb_jhOi2TyLzOdWZb_y7A==/109951169493247841.jpg?param=130y130'
    },
    {
      name: '透明',
      artist: 'G.E.M. 邓紫棋',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/透明 - G.E.M. 邓紫棋.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/G.E.M. 邓紫棋《透明》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/KTo5oSxH3CPA5PBTeFKDyA==/109951164581432409.jpg?param=130y130'
    },
    {
      name: '在他乡',
      artist: '小阿枫',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/在他乡 - 小阿枫.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小阿枫《在他乡》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220124/20220124175306134510.jpg'
    },
    {
      name: '世间万物不及你 ',
      artist: '林小暗、Jin',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/世间万物不及你_MQ - 林小暗、Jin.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/林小暗&Jin《世间万物不及你》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20201112/20201112162904237164.jpg'
    },
    {
      name: '满天星辰不及你 ',
      artist: 'ycccc',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/满天星辰不及你 - ycccc.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/ycccc《满天星辰不及你》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/yJ5Y5xZL8Cv5i_hCR5ATUg==/109951169395356968.jpg?param=130y130'
    },
    {
      name: '像鱼,没有眼泪',
      artist: '庄东茹 - ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/像鱼,没有眼泪_MQ - 庄东茹.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/庄东茹《像鱼，没有眼泪》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/GmHk3FOtoQNeEZtQZruTOA==/109951169219403752.jpg?param=130y130'
    },
    {
      name: '赤伶',
      artist: '孙鹏凯',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/赤伶 - 孙鹏凯.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/孙鹏凯 - 赤伶 (300114259).lrc',
      cover:
        'https://p2.music.126.net/NKFduJKtT_PyCxRXzF9f5A==/109951165849754921.jpg?param=130y130'
    },
    {
      name: '多情种',
      artist: '要不要买菜',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/多情种 - 要不要买菜.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/徐泽（要不要买菜）《多情种》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/ZYgsZKTn5dclbcDVrLnxdw==/109951164442715619.jpg?param=130y130'
    },
    {
      name: '妈妈说过的话',
      artist: '程佳佳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/妈妈说过的话 - 程佳佳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/程佳佳《妈妈说过的话》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220507/20220507180843613951.jpg'
    },
    {
      name: '患者',
      artist: '贺子玲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/患者 - 贺子玲.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/贺子玲《患者》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211110/20211110164350302390.jpg'
    },
    {
      name: '飞跃经济舱',
      artist: 'GAI周延,功夫胖,布瑞吉Bridge',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/飞跃经济舱 - GAI周延、功夫胖、布瑞吉Bridge.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/GAI周延&功夫胖&布瑞吉Bridge《飞跃经济舱》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/crKNUUNZOpqOQWzeGn94EA==/109951167797488296.jpg?param=130y130'
    },
    {
      name: '大可不必 ',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/大可不必_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/苏星婕《大可不必》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211111/20211111190402938611.jpg '
    },
    {
      name: '镜中花',
      artist: '夏婉安',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/镜中花 - 夏婉安.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/夏婉安《镜中花》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221204/20221204154328267978.jpg'
    },
    {
      name: '盗爱者',
      artist: '罗杨栩栩,初月',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/盗爱者 - 罗杨栩栩,初月.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/罗杨栩栩&初月《盗爱者》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220708/20220708173734484768.jpg'
    },
    {
      name: '那个夏天',
      artist: '袁小朵',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/那个夏天 - 袁小朵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/袁小朵《那个夏天》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/YjnGC169DH8YdjSWhHJRzQ==/109951165165720561.jpg?param=130y130'
    },
    {
      name: '青丝宛如画 ',
      artist: '叫宝宝',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/青丝宛如画 - 叫宝宝.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/叫宝宝《青丝宛如画》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220923/20220923180346348352.jpg'
    },
    {
      name: '和光同尘',
      artist: '周深',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/和光同尘 (2021第二届腾讯音乐娱乐盛典现场) - 周深.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/周深 - 和光同尘 (2021第二届腾讯音乐娱乐盛典现场) (92213984).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20201218/20201218161202744172.jpg'
    },
    {
      name: '最美的瞬间 ',
      artist: '弹棉花的小花',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最美的瞬间 - 弹棉花的小花.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/弹棉花的小花《最美的瞬间》[Mp3_Lrc].lrc',
      cover:
        'https://p1.music.126.net/GOOBYAtvi4bc2CQ8kYvIzA==/109951169169137419.jpg?param=130y130'
    },
    {
      name: '星光派对 ',
      artist: '赵希予',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星光派对 - 赵希予.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/赵希予《星光派对》[Mp3_Lrc].lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211102/20211102164402270162.jpg'
    },
    {
      name: '纸上天下',
      artist: '戾格、灼夭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/纸上天下 - 戾格、灼夭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/戾格&灼夭&小田音乐社《纸上天下》[Mp3_Lrc].lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210428/20210428162853769081.jpg'
    },
    {
      name: '我的余温 ',
      artist: '云汐',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我的余温 - 云汐.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/云汐《我的余温》[Mp3_Lrc].lrc',
      cover:
        'https://p2.music.126.net/6_KpdiiXvOrIAUP5Fy6mtg==/109951167898532368.jpg?param=130y130'
    },
    {
      name: '金玉良缘',
      artist: '付雪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/金玉良缘 - 付雪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/金玉良缘 - 付雪_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20211102/20211102151101113356.jpg'
    },
    {
      name: '风最懂 ',
      artist: '夏婉安',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风最懂 - 夏婉安.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风最懂 - 夏婉安_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/eNmluznehhiYqkggLX6yJg==/109951167849095548.jpg?param=130y130'
    },
    {
      name: '星河入海',
      artist: '尹昔眠 - ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星河入海_MQ - 尹昔眠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星河入海 - 尹昔眠_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210629/20210629202327823099.jpg'
    },
    {
      name: '爱怎么转移',
      artist: '山止川行,庄淇文29',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/爱怎么转移 - 山止川行,庄淇文29.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/爱怎么转移 - 山止川行&庄淇玟29_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/vMHUks_JVbpfzXPF_tJxow==/109951168352201136.jpg?param=177y177'
    },
    {
      name: '单人券',
      artist: '张齐山DanieL',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/单人券 - 张齐山DanieL.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/单人券 - 张齐山_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/TAZt9kP0VJNxVGpkWwcNYw==/109951168086232758.jpg?param=130y130'
    },
    {
      name: '心动通告',
      artist: '泽典',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/心动通告 - 泽典.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/心动通告 - 泽典_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220303/20220303114448231838.jpg'
    },
    {
      name: '把回忆拼好给你',
      artist: 'cici',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/把回忆拼好给你 - cici_.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/把回忆拼好给你 - cici__www.eev3.com (1).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220408/20220408182427184973.jpg'
    },
    {
      name: '白月光与朱砂痣',
      artist: '大籽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/白月光与朱砂痣 - 大籽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/白月光与朱砂痣 - 大籽_www.eev3.com.lrc',
      cover:
        'https://img.flmp3.pro/image/20240717/20240717124349_1650.webp'
    },
    {
      name: '收敛',
      artist: '不够',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/收敛 - 不够.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/收敛 - 不够_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/B-iwvcPT9gTj7Wv3fQ3png==/109951164853155233.jpg?param=130y130'
    },
    {
      name: '奔赴星空',
      artist: '尹昔眠',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/奔赴星空 - 尹昔眠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/奔赴星空 - 尹昔眠_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210528/20210528214150729583.jpg'
    },
    {
      name: 'Falling下坠',
      artist: '吴岱林',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Falling下坠 - 吴岱林.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Falling下坠 - 吴岱林_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/mtGuLmfaUXZLojB2OcqjWQ==/109951166898716163.jpg?param=130y130'
    },
    {
      name: '就忘了吧 ',
      artist: '1K',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/就忘了吧 - 1K.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/就忘了吧 - 1K_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220228/20220228191411244500.jpg'
    },
    {
      name: '花溪四杯 ',
      artist: '南栖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花溪四杯 - 南栖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/花溪四杯 - 南栖&小田音乐社_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220506/20220506100959274352.jpg'
    },
    {
      name: '姑娘在远方',
      artist: '柯柯柯啊',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/姑娘在远方 - 柯柯柯啊.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/姑娘在远方 - 柯柯柯啊_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/_AHbNGkhx_bos9mgPPRtQg==/109951169493247793.jpg?param=130y130'
    },
    {
      name: '文爱',
      artist: 'CG、贺敬轩',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/文爱 - CG,贺敬轩.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/文爱 - CG&贺敬轩_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/rocYnJRgaKcrxFk95Mxs0Q==/109951166195417913.jpg?param=130y130'
    },
    {
      name: '一个人挺好 ',
      artist: '孟颖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一个人挺好 - 孟颖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/一个人挺好 - 孟颖_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20191224/20191224170717617635.jpg'
    },
    {
      name: '身后 ',
      artist: 'space x',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/身后 - space x.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/身后 - 千屿&space x_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210907/20210907120554891750.jpg'
    },
    {
      name: '察觉',
      artist: '音格概念',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/察觉 - 音格概念.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/察觉 - 音格概念&逸霄_www.eev3.com.lrc',
      cover:
        'https://is1-ssl.mzstatic.com/image/thumb/Music113/v4/5d/b2/ba/5db2ba25-e4e1-91b4-4075-056831d1fef0/cover.jpg/1200x1200bf-60.jpg'
    },
    {
      name: '再回眸',
      artist: '指尖笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/再回眸 - 指尖笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/再回眸 - 指尖笑_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210616/20210616114903999505.jpg'
    },
    {
      name: '喜欢就争取',
      artist: '任舒瞳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/喜欢就争取 - 任舒瞳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/喜欢就争取 - 任舒瞳_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/RDz-25b6_-QQbxUiwUTg-Q==/109951167077987857.jpg?param=130y130'
    },
    {
      name: '山茶花读不懂白玫瑰',
      artist: 'Lil笑笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/山茶花读不懂白玫瑰 - Lil笑笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/山茶花读不懂白玫瑰 - 梨笑笑_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/tOzvCx5qlzp_XY69vJywTg==/109951169493275918.jpg?param=130y130'
    },
    {
      name: '万里晴空',
      artist: '山止川行',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/万里晴空 - 山止川行.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/万里晴空 - 山止川行&覆予_www.eev3.com.lrc',
      cover:
        'https://th.bing.com/th/id/OIP.Ew0sg0dj2IMj9fScK3m_5AHaHa?rs=1&pid=ImgDetMain'
    },
    {
      name: '风吹一夏',
      artist: 'DP龙猪、Swei水、Rays陈袁',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风吹一夏_MQ - DP龙猪、Swei水、Rays陈袁.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/DP龙猪／Swei水／Rays陈袁 - 风吹一夏 (313028216).lrc',
      cover:
        'https://p2.music.126.net/C6Sivb-S0AsU-Jg1FtHWrg==/109951166067538191.jpg?param=130y130'
    },
    {
      name: '外婆说 ',
      artist: '程jiajia',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/外婆说 - 程jiajia.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/外婆说 - 程佳佳_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210901/20210901143131579109.jpg'
    },
    {
      name: '雨过天不晴',
      artist: '柯柯柯啊',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雨过天不晴 - 柯柯柯啊.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/雨过天不晴 - 柯柯柯啊_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/5bqKKSxhI2w-LCicrqZPvg==/109951168483915678.jpg?param=130y130'
    },
    {
      name: '空长叹',
      artist: '伊格赛听',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/空长叹 - 伊格赛听.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/空长叹 - 伊格赛听&锦零_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/V82VNxRYIoltB5EOnEFwFw==/109951166214515230.jpg?param=177y177'
    },
    {
      name: '赦免',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/赦免_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/赦免 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/DlxM4TY16s05iUG0NUGJrw==/109951168067900402.jpg?param=130y130'
    },
    {
      name: '星空剪影',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星空剪影 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星空剪影 - 蓝心羽_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/zRtQgzngffDl9Y4rsGuk1w==/109951165402919398.jpg?param=130y130'
    },
    {
      name: '放晴',
      artist: '王忻辰、苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/放晴 - 王忻辰、苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/放晴 - 王忻辰&苏星婕_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/1ORBcQsZ3DjGqF11rAleSw==/109951167126243390.jpg?param=177y177'
    },
    {
      name: '多情客',
      artist: '指尖笑',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/多情客 - 指尖笑.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/多情客 - 指尖笑_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/8cA5hkfdbwDfusoHZsDE-Q==/109951167076734781.jpg?param=130y130'
    },
    {
      name: '迟来的情话 (女声版)',
      artist: 'Z kaaai - ',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/迟来的情话 (女声版) - Z kaaai.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Zkaaai - 迟来的情话 (122635390).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221207/20221207132630578281.jpg'
    },
    {
      name: '春风吹',
      artist: '一壬',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/春风吹 - 一壬.mp3',
      lrc: '',
      cover:
        'https://th.bing.com/th/id/OIP.yrh9o3imprI06oJX9saq2QHaEo?rs=1&pid=ImgDetMain'
    },
    {
      name: '若把你',
      artist: '旺仔加真露',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/若把你 - 旺仔加真露.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔加真露 - 若把你 (383746123).lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20221115/20221115165006494463.jpg'
    },
    {
      name: '难入山海 ',
      artist: '李常超',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/难入山海 - 李常超（Lao乾妈）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/难入山海 - 李常超（Lao乾妈）_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20230428/20230428182404775626.jpg'
    },
    {
      name: '逐影',
      artist: '黑崎子',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/逐影 - 黑崎子.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/逐影 - 黑崎子_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211008/20211008100702762306.jpg'
    },
    {
      name: '游京',
      artist: '小淅儿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/游京 - 小淅儿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/游京 - 小淅儿_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/dLFeCJfRhzVYp3MOVmwBvw==/109951164387698372.jpg?param=130y130'
    },
    {
      name: '银河与星斗',
      artist: 'yihuik苡慧',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/银河与星斗 - yihuik苡慧.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/银河与星斗 - yihuik苡慧_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/4fGSYK5S1SskrZjkyCJfJw==/109951166035185227.jpg?param=177y177'
    },
    {
      name: '温柔孤岛',
      artist: 'IN-K,苏星婕,吴瑭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/温柔孤岛 - IN-K,苏星婕,吴瑭.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/温柔孤岛 - IN-K&苏星婕&吴瑭_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210521/20210521132153890765.jpg'
    },
    {
      name: '枕边童话',
      artist: '小田音乐社',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/枕边童话 - 小田音乐社.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/枕边童话 - 小田音乐社&傲七爷(江偌绮)_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/sG8jg5UOPUkEvH35Me5fKA==/109951165071053342.jpg?param=130y130'
    },
    {
      name: '时空缝隙 ',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/时空缝隙 - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/时空缝隙 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210309/20210309162222240508.jpg'
    },
    {
      name: '水晶上的牧羊女',
      artist: '张齐山DanieL',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/水晶上的牧羊女 - 张齐山DanieL.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/水晶上的牧羊女 - 张齐山_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230625/20230625150922124607.jpg'
    },
    {
      name: '孤独颂歌',
      artist: '戴羽彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/孤独颂歌 - 戴羽彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/孤独颂歌 - 戴羽彤_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220427/20220427170912217613.jpg'
    },
    {
      name: '年轮 ',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/年轮 - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/年轮 - 旺仔小乔_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210818/20210818200844861430.jpg'
    },
    {
      name: '因果',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/因果 - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/因果 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221121/20221121152002211623.jpg'
    },
    {
      name: '风过谢桃花',
      artist: '汐音社',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风过谢桃花 - 汐音社.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风过谢桃花 - 司南&国风新语&汐音社_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/9xOEW6dRPRDIEtZrKMO9pQ==/109951165870371224.jpg?param=177y177'
    },
    {
      name: '啷个哩个啷',
      artist: '鹏泊',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/啷个哩个啷 - 鹏泊.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/啷个哩个啷 - 鹏泊_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/ZnFe6Uj1Lmr_HrAjDbe02Q==/1394180751920871.jpg?param=130y130'
    },
    {
      name: '归去来兮 ',
      artist: '鸾音社',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/归去来兮 - 鸾音社.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/归去来兮 - 鸾音社_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200909/20200909115113718157.jpg'
    },
    {
      name: '经纬线 ',
      artist: '豆芽鱼、 Aech洪文豪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/经纬线 - 豆芽鱼,Aech洪文豪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/经纬线 - Aech洪文豪&庄东茹_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211021/20211021163306807630.jpg'
    },
    {
      name: '厚颜无耻',
      artist: '曲肖冰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/厚颜无耻 - 曲肖冰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/厚颜无耻 - 曲肖冰_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/82G125H164ZPryleQLHg3Q==/109951164611990638.jpg?param=177y177'
    },
    {
      name: '0222心动',
      artist: 'Bomb比尔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/0222心动-比尔的歌 - Bomb比尔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/0222心动-比尔的歌 - Bomb比尔_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/7B8sujEckvlvdb6JcqkGdw==/109951169574939913.jpg?param=177y177'
    },
    {
      name: '灰色的忧伤',
      artist: '夏婉安',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/灰色的忧伤 - 夏婉安.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/灰色的忧伤 - 夏婉安_www.eev3.com.lrc',
      cover:
        'https://bkimg.cdn.bcebos.com/pic/78310a55b319ebc4b745ff35a573d8fc1e178a82b858?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70'
    },
    {
      name: '无人看守 ',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/无人看守_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/无人看守 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/0aOoZU8r79rB0Nr9iwJ-zw==/109951167759627751.jpg?param=177y177'
    },
    {
      name: '起风的后来',
      artist: '王忻辰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/起风的后来 - 王忻辰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/起风的后来 - 王忻辰_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220801/20220801162950190222.jpg'
    },
    {
      name: '晚风也思你',
      artist: 'L（桃籽）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/晚风也思你 - L（桃籽）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/晚风也思你 - L（桃籽）_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/UqzYU6l5B4wuHzlZnYrV5g==/109951164788216205.jpg?param=640y300'
    },
    {
      name: '埋葬爱 ',
      artist: 'Dollyy99',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/埋葬爱 - Dollyy99.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/埋葬爱 - dollyy99_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220311/20220311152334931993.jpg'
    },
    {
      name: '18dB ',
      artist: '吴子健REmi、小蓝背心',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/18dB - 吴子健REmi、小蓝背心.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/18dB - 吴子健REmi&小蓝背心_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/XpUU6vMvhF0CJXxa8et_sg==/109951166368580753.jpg?param=130y130'
    },
    {
      name: '堕',
      artist: '南葵',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/堕 - 南葵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/堕 - 南葵_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220721/20220721143131949168.jpg'
    },
    {
      name: '潮汐 (Natural)',
      artist: 'IN-K、安苏羽、傅梦彤',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/潮汐 (Natural) - IN-K,安苏羽,傅梦彤.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/IN-K／安苏羽／傅梦彤 - 潮汐(Natural) (0.9x版) (386056085).lrc',
      cover:
        'https://p2.music.126.net/im6sWzE3npCTVcwOwC_WyA==/109951168162930839.jpg?param=130y130'
    },
    {
      name: '孤牢',
      artist: '吕帅',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/孤牢 - 吕帅.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/孤牢 - 吕帅_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220520/20220520144228501107.jpg'
    },
    {
      name: '时光背面的我',
      artist: '刘至佳、韩瞳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/时光背面的我 - 刘至佳、韩瞳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/时光背面的我 - 刘至佳&韩瞳_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/VOcPF6uCGAlwESFyyGqHrg==/109951166141514551.jpg?param=130y130'
    },
    {
      name: '大喜 ',
      artist: '泠鸢yousa、音阙诗听',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/大喜 - 泠鸢yousa,音阙诗听.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/大喜 - 泠鸢yousa&音阙诗听_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/4FiWdxnfnGfq0RsXpJGTXw==/109951165772596480.jpg?param=130y130'
    },
    {
      name: '忘了',
      artist: '周林枫',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/忘了 - 周林枫.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/《忘了 (周林枫) 》：有点好听~很难不喜欢 - 音乐全能鹅_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230302/20230302152043750458.jpg'
    },
    {
      name: '危险派对',
      artist: '王以太、刘至佳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/危险派对_MQ - 王以太、刘至佳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/危险派对 - 王以太&刘至佳_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/GpnLproqUUyc4xmYKpRFcQ==/109951166516282895.jpg?param=130y130'
    },
    {
      name: '情结 ',
      artist: '你们的好朋友大雨',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/情结 - 你们的好朋友大雨.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/情结 - 你们的好朋友大雨_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210617/20210617115535849174.jpg'
    },
    {
      name: '不如 ',
      artist: '秦海清',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/不如 - 秦海清.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/不如 - 秦海清_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210720/20210720232605377812.jpg'
    },
    {
      name: '一花一剑',
      artist: '书笙',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/一花一剑 - 书笙.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/一花一剑 - 邓寓君(等什么君)_www.eev3.com (1).lrc',
      cover:
        'https://p2.music.126.net/ftj6EcoW3hQAPKdj_iirpg==/109951165393659895.jpg?param=130y130'
    },
    {
      name: '樱花与你 ',
      artist: '洪思雨',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/樱花与你 - 洪思雨.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/樱花与你 - 洪思雨_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20210802/20210802173805813260.jpg'
    },
    {
      name: '最美的伤口（女版）',
      artist: '毛乖乖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/最美的伤口（女版） - 毛乖乖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/最美的伤口 - 毛乖乖_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20200407/20200407190615422217.jpg'
    },
    {
      name: '放个大招给你看',
      artist: '永彬Ryan.B',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/放个大招给你看 - 永彬Ryan.B.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/放个大招给你看 (cover_ 永彬Ryan.B) - 佯佯_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/dM5UkUR6auof6V6Q8CdloQ==/109951164173169271.jpg?param=130y130'
    },
    {
      name: '快要遇见你 ',
      artist: '许言',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/快要遇见你 - 许言.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/许言 - 快要遇见你 (314737409).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210617/20210617164004948055.jpg'
    },
    {
      name: '谪仙',
      artist: '伊格赛听',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/谪仙 - 伊格赛听.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/谪仙 - 伊格赛听&叶里_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/X-ZQ6wkyaL9cJTiyxmDhuw==/109951164680974796.jpg?param=130y130'
    },
    {
      name: '清空',
      artist: '麦小兜',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/清空 - 麦小兜.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/清空 - 麦小兜_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210125/20210125184856986373.jpg'
    },
    {
      name: '雨下一整夜',
      artist: '吴子健REmi',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雨下一整夜 - 吴子健REmi.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/雨下一整夜 - 吴子健REmi_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/sADTKSVsvwfmEu4GMDo6KA==/109951168002655113.jpg?param=130y130'
    },
    {
      name: '鲸落万物生',
      artist: 'Mukyo木西',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/鲸落万物生 - Mukyo木西.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/鲸落万物生 - 灼夭&小田音乐社_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/jOKnPiD5XRkPSa2gVjlnDA==/109951168466779951.jpg?param=130y130'
    },
    {
      name: '风铃遇海',
      artist: '绝世小雪琪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风铃遇海 - 绝世小雪琪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风铃遇海 - 绝世小雪琪_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220805/20220805094101355638.jpg'
    },
    {
      name: '爱是无畏的冒险',
      artist: '艺煊呀',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/爱是无畏的冒险 - 艺煊呀.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/艺煊呀 - 爱是无畏的冒险 (379359868).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221017/20221017173549970661.jpg'
    },
    {
      name: '樱花树下的约定',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/樱花树下的约定_MQ - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/旺仔小乔 - 樱花树下的约定 (353863582).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220510/20220510121422962846.jpg'
    },
    {
      name: '落叶与秋',
      artist: '刘烁七、梓豪',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落叶与秋_MQ - 刘烁七、梓豪.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/落葉與秋-劉爍七_梓豪『一聲 - 深深的蓝_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220105/20220105174532528754.jpg'
    },
    {
      name: '失眠记忆',
      artist: '陈子晴',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/失眠记忆 - 陈子晴.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/失眠记忆 - 陈子晴_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211203/20211203163818898411.jpg'
    },
    {
      name: '燕无歇',
      artist: '七叔（叶泽浩）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/燕无歇 - 是七叔呢.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/七叔（叶泽浩） - 燕无歇 (280863340).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230117/20230117182051970378.jpg'
    },
    {
      name: '落日与晚风',
      artist: 'IN-K,王忻辰,苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/落日与晚风 - IN-K,王忻辰,苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/落日与晚风 - IN-K&王忻辰&苏星婕_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230710/20230710114917648699.jpg'
    },
    {
      name: '空山新雨后 ',
      artist: '音阙诗听,锦零',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/空山新雨后 - 音阙诗听,锦零.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/空山新雨后 - 音阙诗听&锦零_www.eev3.com (1).lrc',
      cover:
        'https://p1.music.126.net/J41DBagRJED1YHcBCMsk3A==/109951169246693269.jpg?param=130y130'
    },
    {
      name: '控制 ',
      artist: '王忻辰、苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/控制 - 王忻辰、苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/控制 - 王忻辰&苏星婕_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20240111/20240111140024572444.jpg'
    },
    {
      name: '多谢 ',
      artist: '陆杰awr',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/多谢 - 陆杰awr.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/多谢 - 陆杰awr_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/t4FR02kTvjwOWJTzt9txNQ==/109951167596235014.jpg?param=130y130'
    },
    {
      name: '开始想念你的口红',
      artist: '猪无能',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/开始想念你的口红 - 猪无能.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/开始想念你的口红 - 猪无能.lrc',
      cover:
        'https://p1.music.126.net/PosCZHGJzLaHS9ovNm7qQw==/109951168097048332.jpg?param=130y130'
    },
    {
      name: '想对你说',
      artist: '赵希予',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/想对你说 - 赵希予.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/想對妳說-趙希予『一步一步靠近妳，想對妳說遇見妳是萬分之一，一點一點擁抱妳， - 深深的蓝_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221014/20221014140428644876.jpg'
    },
    {
      name: '航行',
      artist: '蒋小呢',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/航行_MQ - 蒋小呢.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/航行 - 蒋小呢_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/S93XtqXKVFHHsJW3xhtTTA==/109951165902761275.jpg?param=130y130'
    },
    {
      name: '风摇海棠 ',
      artist: '音阙诗听,Vaniah维',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/音阙诗听Vaniah维_-_风摇海棠_(1)1.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风摇海棠 - 音阙诗听&Vaniah维_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/J41DBagRJED1YHcBCMsk3A==/109951169246693269.jpg?param=130y130'
    },
    {
      name: '会不会太晚',
      artist: 'Li-2c（李楚楚）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/会不会太晚 - Li-2c（李楚楚）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/会不会太晚 - Li-2c（李楚楚）_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210611/20210611155838175932.jpg'
    },
    {
      name: '失眠播报',
      artist: '林晨阳',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/街角Master MMM - 林晨阳.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/林晨阳 - 失眠播报 (288440146).lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230118/20230118114040258870.jpg'
    },
    {
      name: '千年酒',
      artist: '南栖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/千年酒 - 南栖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/千年酒 - 南栖_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221102/20221102170350650845.jpg'
    },
    {
      name: '对手戏2000',
      artist: '艾福杰尼',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/对手戏2000 - 艾福杰尼.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/对手戏2000 - 艾福杰尼_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220519/20220519164041260011.jpg'
    },
    {
      name: '星星住进你眼睛',
      artist: '刘至远,Mimmi',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星星住进你眼睛 - 刘至远,Mimmi.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星星住进你眼睛 - 刘至远&Mimmi_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220530/20220530164629462748.jpg'
    },
    {
      name: '平行线',
      artist: 'Vicetone、WILLIM缪维霖、黄霄雲',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/平行线 - Vicetone、WILLIM缪维霖、黄霄雲.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/平行线 (Wish You Were Here) - Vicetone&WILLIM缪维霖&黄霄雲_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/N-WxRHWqARxjQnEjdNWr6Q==/109951166226272712.jpg?param=130y130'
    },
    {
      name: '怎配',
      artist: '许路遥',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/怎配 - 许路遥.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/怎配 - 许路遥_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221130/20221130162251644954.jpg'
    },
    {
      name: '再见 ',
      artist: '一栗莎子',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/再见 - 一栗莎子.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/再见 - 一栗莎子_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220420/20220420182916903611.jpg'
    },
    {
      name: '差别对待',
      artist: 'Ssweetxin_、宋厦',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/差别对待 - Ssweetxin_、宋厦.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/差别对待 - Y-D&宋厦&Ssweetxin__www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211020/20211020145120305304.jpg'
    },
    {
      name: '为你心动',
      artist: 'Zyboy忠宇,芬芬',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/为你心动 - Zyboy忠宇,芬芬.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/为你心动 - Zyboy忠宇&芬芬_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20221114/20221114170808197391.jpg'
    },
    {
      name: '海市蜃楼',
      artist: '一颗狼星',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/海市蜃楼 - 一颗狼星.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/海市蜃楼 - 一颗狼星_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220128/20220128162133443548.jpg'
    },
    {
      name: '嫁',
      artist: 'L（桃籽）, 周林枫, 三楠',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/嫁 - L（桃籽）,周林枫,三楠.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/嫁 - L（桃籽）&周林枫&三楠_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220617/20220617154426303960.jpg'
    },
    {
      name: '风是从哪儿来Wu',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/风是从哪儿来wu (《我们打着光脚在风车下跑,手上的狗尾巴草摇啊摇》)_MQ - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/风是从哪儿来wu(《我们打着光脚在风车下跑，手上的狗尾巴草摇啊摇》) - 旺仔小乔_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220822/20220822215649847899.jpg'
    },
    {
      name: '囍与悲',
      artist: '三楠, 周林枫, L（桃籽）',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/囍与悲 - 三楠,周林枫,L（桃籽）.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/囍与悲 - 三楠&周林枫&L（桃籽）_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211101/20211101152206677974.jpg'
    },
    {
      name: '或许怪我',
      artist: 'LZ轮子, Jady',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/或许怪我 - LZ轮子、Jady.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/或许怪我 - LZ轮子&Jady_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220617/20220617155018965763.jpg'
    },
    {
      name: '小雨天气',
      artist: 'yihuik苡慧、Kui Kui、十七草',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/小雨天气_MQ - yihuik苡慧、Kui Kui、十七草.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小雨天气 - yihuik苡慧&Kui Kui&十七草_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/a_ZFlznM1rbSuvqzx9N8XQ==/109951166554719468.jpg?param=130y130'
    },
    {
      name: '小雨天气 ',
      artist: 'Kui Kui、徐梦圆',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/小雨天气 - Kui Kui、徐梦圆.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/小雨天气 - Kui Kui&徐梦圆_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210908/20210908142601418278.jpg'
    },
    {
      name: '时光背面的我 ',
      artist: '鱼闪闪BLING',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/时光背面的我 - 鱼闪闪BLING.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/时光背面的我 - 鱼闪闪BLING_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210928/20210928211805635874.jpg'
    },
    {
      name: '雾起海岸',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/雾起海岸 - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/雾起海岸 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/7Dal-_HczDYt6r3JCAj8QA==/109951169032592525.jpg?param=130y130'
    },
    {
      name: '等一场大雨',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/等一场大雨_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/等一场大雨 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210519/20210519111413212811.jpg'
    },
    {
      name: '花败',
      artist: '旺仔小乔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/花败 - 旺仔小乔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/花败 - 旺仔小乔_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210326/20210326200356866802.jpg'
    },
    {
      name: '星辰不坠落 ',
      artist: '蓝心羽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/星辰不坠落 - 蓝心羽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/星辰不坠落 - 蓝心羽_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/cngZBISN9L_LoyN9m0zU9w==/109951166443776471.jpg?param=130y130'
    },
    {
      name: '骁 ',
      artist: '井胧, 井迪儿',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/骁 - 井胧,井迪儿.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/骁 - 井胧&井迪儿_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/p7EpQLr6rEsA2rKyjyZn9Q==/109951167101847101.jpg?param=130y130'
    },
    {
      name: '迷失幻境 ',
      artist: 'IN-K, 王忻辰',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/迷失幻境 - IN-K,王忻辰.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/迷失幻境 - IN-K&王忻辰_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20201103/20201103145014828153.jpg'
    },
    {
      name: '有没有动心',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/有没有动心_MQ - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/有没有动心 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/tFh0ATDvy3FBmJq2DYSlbQ==/109951168070715951.jpg?param=130y130'
    },
    {
      name: '删除忘记',
      artist: '苏星婕',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/删除忘记 - 苏星婕.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/删除忘记 - 苏星婕_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20211008/20211008153539516693.jpg'
    },
    {
      name: '等不来花开',
      artist: 'pro',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/等不来花开 - pro.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/等不来花开 - pro_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220621/20220621233002835569.jpg'
    },
    {
      name: '智者不入爱河',
      artist: 'co这个李文',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/智者不入爱河 - coco这个李文.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/智者不入爱河 - coco这个李文_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220217/20220217102326711933.jpg'
    },
    {
      name: '守望你的心',
      artist: '胖虎',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/胖虎_-_守望你的心1.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/守望你的星 - 胖虎（董欣怡）_www.eev3.com.lrc',
      cover:
        'https://p2.music.126.net/ZpwKyufuBxH3rKXQk1UBPQ==/109951167315453890.jpg?param=130y130'
    },
    {
      name: '菲律宾没有雪马尼拉没有爱',
      artist: '杨朵',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/菲律宾没有雪马尼拉没有爱 - 杨朵.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/菲律宾没有雪马尼拉没有爱 - 杨朵_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230105/20230105131804907093.jpg'
    },
    {
      name: '物理反应',
      artist: '李尖尖',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/物理反应 - 李尖尖.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/物理反应 - 李尖尖_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220314/20220314155529574909.jpg'
    },
    {
      name: '得不到的就更加爱 ',
      artist: '古阿扎',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/古阿扎-得不到的就更加爱 - 古阿扎.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/得不到的就更加爱 - 古阿扎_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20220112/20220112183204122596.jpg'
    },
    {
      name: '桃子汽水',
      artist: '小根号',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/桃子汽水 - 小根号.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/桃子汽水 - 小根号_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210528/20210528171823439335.jpg'
    },
    {
      name: '想在你身边永远',
      artist: '温浪漫',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/想在你身边 永远 - 温浪漫.mp3',
      lrc: '',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/240/20230420/20230420161915980865.jpg'
    },
    {
      name: '椿',
      artist: '超人不会飞',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/椿 - 超人不会飞.mp3',
      lrc: '',
      cover:
        'https://p1.music.126.net/4E5b_0eDTiMCzYKiVSAerw==/19165587184063665.jpg?param=130y130'
    },
    {
      name: '彩色翅膀(四郎版)',
      artist: '奶茶小肥仔',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/彩色翅膀 - 奶茶小肥仔.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/奶茶小肥仔 - 彩色翅膀 (四郎版) (382158073).lrc',
      cover:
        'https://p2.music.126.net/51yHyfcm7DoCKO4t9UlItg==/109951168020566897.jpg?param=130y130'
    },
    {
      name: 'Cu Chill Thoi (慢速版)',
      artist: '温浪漫',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/Cu Chill Thoi - 温浪漫.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/Cu Chill Thoi - 温浪漫_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/stdmusic/20230210/20230210121228684280.jpg'
    },
    {
      name: '阿里嘎多美羊羊桑 ',
      artist: '看海不入海',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/阿里嘎多美羊羊桑 - 看海不入海.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/阿里嘎多美羊羊桑 - 看海不入海_www.eev3.com.lrc',
      cover:
        'https://p1.music.126.net/pi8qLMpmIgFwMVjY4cclIQ==/109951168250121247.jpg?param=130y130'
    },
    {
      name: '手心的蔷薇',
      artist: '南辰Music',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/手心的蔷薇 - 南辰Music.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/In The Shadow Of The Sun - 南辰Music_www.eev3.com.lrc',
      cover:
        'https://imge.kugou.com/stdmusic/20220225/20220225164922635412.jpg'
    },
    {
      name: '漠河舞厅·2022',
      artist: '柳爽',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/正在播放- 漠河舞厅·2022 - 柳爽.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/漠河舞厅·2022-柳爽.lrc',
      cover:
        'https://p2.music.126.net/m8BMzRWR53lMu2uaMYV2mA==/109951166609630672.jpg?param=130y130'
    },
    {
      name: '我的少年 (片段)',
      artist: 'npCswag',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/我的少年 (57秒片段) - npCswag.mp3',
      lrc: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/geci/我的少年 - npCswag_www.eev3.com.lrc',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20181015/20181015145837478.jpg'
    },
    {
      name: '别说亏欠 (DJ版)',
      artist: '吴瑭',
      url: 'https://gcore.jsdelivr.net/gh/QiaoGT/Telegraph-Image@main/file/别说亏欠 (DJ版) - 吴瑭.mp3',
      lrc: '',
      cover:
        'https://imgessl.kugou.com/stdmusic/20210831/20210831170912962860.jpg'
    },
    {
      name: 'F.I.R vs 彭佳慧 - DJ心之火',
      artist: 'Dj阿七',
      url: 'https://mp4.djuu.com/c4/26/2023/daa4eb96933a9946.m4a',
      cover:
        'https://p2.music.126.net/sYFiRAviIWhHobHLAvU0zQ==/109951166240234857.jpg?param=140y140'
    },
    {
      name: '梁静茹 - DJ暖暖',
      artist: 'Dj京仔',
      url: 'https://mp4.djuu.com/c4/41/2023/4853342968aa0a2c.m4a',
      cover:
        'https://imgessl.kugou.com/uploadpic/softhead/400/20230525/20230525195310564.jpg'
    },
    {
      name: '张碧晨 - DJ不要忘记我爱你',
      artist: 'DJ志军',
      url: 'https://mp4.djuu.com/c115/19066/117/2023/b4ca769c711f3628.m4a',
      cover:
        'https://img.djuu.com/cover/2023928/feeef0cf.jpg'
    },
    {
      name: '印子月 - DJ落空',
      artist: 'DjSjun',
      url: 'https://mp4.djuu.com/c4/26/2023/717dfd9bf45fccd2.m4a',
      cover:
        'https://p2.music.126.net/k0DBVCvajyEDY9cDAn0C-g==/109951167898411727.jpg?param=130y130'
    }
  ],
  MUSIC_PLAYER_METING: process.env.NEXT_PUBLIC_MUSIC_PLAYER_METING || false, // 是否要开启 MetingJS，从平台获取歌单。会覆盖自定义的 MUSIC_PLAYER_AUDIO_LIST，更多配置信息：https://github.com/metowolf/MetingJS
  MUSIC_PLAYER_METING_SERVER:
    process.env.NEXT_PUBLIC_MUSIC_PLAYER_METING_SERVER || 'netease', // 音乐平台，[netease, tencent, kugou, xiami, baidu]
  MUSIC_PLAYER_METING_ID:
    process.env.NEXT_PUBLIC_MUSIC_PLAYER_METING_ID || '60198', // 对应歌单的 id
  MUSIC_PLAYER_METING_LRC_TYPE:
    process.env.NEXT_PUBLIC_MUSIC_PLAYER_METING_LRC_TYPE || '1', // 已废弃！！！可选值： 3 | 1 | 0（0：禁用 lrc 歌词，1：lrc 格式的字符串，3：lrc 文件 url）

  // 一个小插件展示你的facebook fan page~ @see https://tw.andys.pro/article/add-facebook-fanpage-notionnext
  FACEBOOK_PAGE_TITLE: process.env.NEXT_PUBLIC_FACEBOOK_PAGE_TITLE || null, // 邊欄 Facebook Page widget 的標題欄，填''則無標題欄 e.g FACEBOOK 粉絲團'
  FACEBOOK_PAGE: process.env.NEXT_PUBLIC_FACEBOOK_PAGE || null, // Facebook Page 的連結 e.g https://www.facebook.com/tw.andys.pro
  FACEBOOK_PAGE_ID: process.env.NEXT_PUBLIC_FACEBOOK_PAGE_ID || '', // Facebook Page ID 來啟用 messenger 聊天功能
  FACEBOOK_APP_ID: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || '' // Facebook App ID 來啟用 messenger 聊天功能 获取: https://developers.facebook.com/
}
