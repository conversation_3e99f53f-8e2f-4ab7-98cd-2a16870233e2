/**
 * 从页面ID中提取语言前缀
 * 支持格式: zh:xxxxx, en:xxxxx 等
 * @param {string} siteId - 站点ID，可能包含语言前缀
 * @returns {string|null} - 语言前缀，如 'zh', 'en' 等，如果没有前缀则返回null
 */
export function extractLangPrefix(siteId) {
  if (!siteId || typeof siteId !== 'string') {
    return null
  }
  
  // 检查是否包含冒号分隔符
  const colonIndex = siteId.indexOf(':')
  if (colonIndex > 0) {
    const prefix = siteId.substring(0, colonIndex)
    // 验证前缀是否为有效的语言代码格式（2-5个字母，可能包含连字符）
    if (/^[a-zA-Z]{2}(-[a-zA-Z]{2,3})?$/.test(prefix)) {
      return prefix
    }
  }
  
  return null
}

/**
 * 从页面ID中提取实际的ID部分（去除语言前缀）
 * @param {string} siteId - 站点ID，可能包含语言前缀
 * @returns {string} - 实际的页面ID
 */
export function extractLangId(siteId) {
  if (!siteId || typeof siteId !== 'string') {
    return siteId
  }
  
  const colonIndex = siteId.indexOf(':')
  if (colonIndex > 0) {
    return siteId.substring(colonIndex + 1)
  }
  
  return siteId
}

/**
 * 获取页面ID的短格式
 * 将完整的UUID格式转换为短格式
 * @param {string} id - 完整的页面ID
 * @returns {string} - 短格式的ID
 */
export function getShortId(id) {
  if (!id || typeof id !== 'string') {
    return id
  }
  
  // 移除连字符，只保留字母数字字符
  return id.replace(/-/g, '')
}

/**
 * 检查字符串是否为有效的页面ID格式
 * @param {string} str - 要检查的字符串
 * @returns {boolean} - 是否为有效的页面ID
 */
export function isValidPageId(str) {
  if (!str || typeof str !== 'string') {
    return false
  }
  
  // 移除可能的语言前缀
  const actualId = extractLangId(str)
  
  // 检查是否为32位的字母数字字符串（Notion ID格式）
  const notionIdRegex = /^[a-zA-Z0-9]{32}$/
  if (notionIdRegex.test(actualId)) {
    return true
  }
  
  // 检查是否为UUID格式
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i
  if (uuidRegex.test(actualId)) {
    return true
  }
  
  return false
}

/**
 * 标准化页面ID格式
 * @param {string} id - 原始页面ID
 * @returns {string} - 标准化后的页面ID
 */
export function normalizePageId(id) {
  if (!id || typeof id !== 'string') {
    return id
  }
  
  // 移除所有连字符和空格
  return id.replace(/[-\s]/g, '').toLowerCase()
}
