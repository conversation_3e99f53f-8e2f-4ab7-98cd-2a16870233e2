<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感谢捐赠者 - 爱心榜单</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: titleGlow 2s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.5); }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 30px rgba(255,255,255,0.8); }
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .thanks-message {
            font-size: 1rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* 第一名 - 最炫酷特效 */
        .rank-1 {
            position: relative;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transform: scale(1.05);
        }

        .rank-1::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            border-radius: 25px;
            z-index: -1;
            filter: blur(10px);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .rank-1 .donor-name {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .rank-1 .crown {
            font-size: 3rem;
            animation: rotate 4s linear infinite;
            display: inline-block;
            margin-right: 15px;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 第二名特效 */
        .rank-2 {
            position: relative;
            background: linear-gradient(135deg, #667eea, #764ba2, #ff6b6b);
            background-size: 300% 300%;
            animation: gradientMove 4s ease infinite, pulse 2s infinite;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
            border: 3px solid #ffd700;
            transform: scale(1.02);
        }

        .rank-2::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
            background-size: 200% 200%;
            animation: goldShine 2s ease infinite;
            border-radius: 18px;
            z-index: -1;
        }

        @keyframes gradientMove {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes goldShine {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes pulse {
            0% { box-shadow: 0 15px 30px rgba(0,0,0,0.3), 0 0 0 0 rgba(255,215,0,0.8); }
            70% { box-shadow: 0 15px 30px rgba(0,0,0,0.3), 0 0 0 15px rgba(255,215,0,0); }
            100% { box-shadow: 0 15px 30px rgba(0,0,0,0.3), 0 0 0 0 rgba(255,215,0,0); }
        }

        .rank-2 .donor-name {
            font-size: 2rem;
            color: #ffd700;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.7), 0 0 10px rgba(255,215,0,0.5); }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.7), 0 0 20px rgba(255,215,0,0.9); }
        }

        .rank-2 .rank-number {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: bold;
            animation: numberSpin 3s linear infinite;
        }

        @keyframes numberSpin {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }

        /* 第三名特效 */
        .rank-3 {
            position: relative;
            background: linear-gradient(135deg, #4facfe, #00f2fe, #43e97b);
            background-size: 300% 300%;
            animation: waveMove 3s ease infinite, bronzeGlow 2.5s infinite;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            border: 2px solid #cd7f32;
            overflow: hidden;
        }

        .rank-3::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmerSweep 2s infinite;
        }

        .rank-3::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #cd7f32, #daa520, #cd7f32);
            background-size: 200% 200%;
            animation: bronzeShine 3s ease infinite;
            border-radius: 14px;
            z-index: -1;
        }

        @keyframes waveMove {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes shimmerSweep {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes bronzeShine {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes bronzeGlow {
            0% { box-shadow: 0 10px 20px rgba(0,0,0,0.2), 0 0 0 0 rgba(205,127,50,0.6); }
            50% { box-shadow: 0 10px 20px rgba(0,0,0,0.2), 0 0 0 8px rgba(205,127,50,0); }
            100% { box-shadow: 0 10px 20px rgba(0,0,0,0.2), 0 0 0 0 rgba(205,127,50,0); }
        }

        .rank-3 .donor-name {
            font-size: 1.8rem;
            color: #cd7f32;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: bronzeTextGlow 2s ease-in-out infinite alternate;
        }

        @keyframes bronzeTextGlow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 8px rgba(205,127,50,0.5); }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 15px rgba(205,127,50,0.8); }
        }

        .rank-3 .rank-number {
            background: linear-gradient(45deg, #cd7f32, #daa520);
            color: white;
            font-weight: bold;
            animation: numberFloat 2s ease-in-out infinite alternate;
        }

        @keyframes numberFloat {
            from { transform: translateY(0px); }
            to { transform: translateY(-3px); }
        }

        /* 普通捐赠者样式 */
        .donor-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .donor-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .donor-name {
            font-size: 1.5rem;
            color: white;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .donor-amount {
            color: #ffd700;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .rank-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }

        /* 头像样式 */
        .avatar {
            display: inline-block;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
            border: 3px solid rgba(255,255,255,0.3);
            transition: transform 0.3s ease;
            vertical-align: middle;
        }

        .avatar:hover {
            transform: scale(1.1);
        }

        /* 第一名头像特效 */
        .rank-1 .avatar {
            border: 3px solid #ffd700;
            box-shadow: 0 0 20px rgba(255,215,0,0.6);
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }

        @keyframes avatarGlow {
            from { box-shadow: 0 0 20px rgba(255,215,0,0.6); }
            to { box-shadow: 0 0 30px rgba(255,215,0,0.9); }
        }

        /* 第二名头像特效 */
        .rank-2 .avatar {
            border: 3px solid #ffd700;
            box-shadow: 0 0 15px rgba(255,215,0,0.5);
            animation: avatarPulse 2s infinite;
        }

        @keyframes avatarPulse {
            0% { box-shadow: 0 0 15px rgba(255,215,0,0.5); }
            50% { box-shadow: 0 0 25px rgba(255,215,0,0.8); }
            100% { box-shadow: 0 0 15px rgba(255,215,0,0.5); }
        }

        /* 第三名头像特效 */
        .rank-3 .avatar {
            border: 3px solid #cd7f32;
            box-shadow: 0 0 12px rgba(205,127,50,0.5);
            animation: avatarShine 3s infinite;
        }

        @keyframes avatarShine {
            0% { box-shadow: 0 0 12px rgba(205,127,50,0.5); }
            50% { box-shadow: 0 0 20px rgba(205,127,50,0.7); }
            100% { box-shadow: 0 0 12px rgba(205,127,50,0.5); }
        }

        /* 默认头像样式 */
        .default-avatar {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            margin-right: 15px;
            border: 3px solid rgba(255,255,255,0.3);
            transition: transform 0.3s ease;
            vertical-align: middle;
        }

        .default-avatar:hover {
            transform: scale(1.1);
        }

        /* 第一名默认头像特效 */
        .rank-1 .default-avatar {
            border: 3px solid #ffd700;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            box-shadow: 0 0 20px rgba(255,215,0,0.6);
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }

        /* 第二名默认头像特效 */
        .rank-2 .default-avatar {
            border: 3px solid #ffd700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 0 15px rgba(255,215,0,0.5);
            animation: avatarPulse 2s infinite;
        }

        /* 第三名默认头像特效 */
        .rank-3 .default-avatar {
            border: 3px solid #cd7f32;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            box-shadow: 0 0 12px rgba(205,127,50,0.5);
            animation: avatarShine 3s infinite;
        }

        /* 调整布局 */
        .donor-info {
            display: inline-block;
            vertical-align: middle;
        }

        .floating-hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .heart {
            position: absolute;
            color: rgba(255,182,193,0.6);
            font-size: 20px;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 收付款图标样式 */
        .payment-icons {
            position: fixed;
            bottom: 60px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .payment-icon {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .payment-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .payment-icon .icon-content {
            font-size: 28px;
            line-height: 1;
        }

        /* 微信图标样式 */
        .wechat-icon {
            background: linear-gradient(135deg, #07c160, #00d976);
            color: white;
        }

        .wechat-icon:hover {
            background: linear-gradient(135deg, #06ad56, #00c269);
        }

        /* 支付宝图标样式 */
        .alipay-icon {
            background: linear-gradient(135deg, #1677ff, #40a9ff);
            color: white;
        }

        .alipay-icon:hover {
            background: linear-gradient(135deg, #1366d9, #3691d9);
        }

        /* 悬浮显示的二维码图片 */
        .payment-qr {
            position: absolute;
            right: 80px;
            top: 50%;
            transform: translateY(-50%);
            width: 200px;
            height: 200px;
            background: white;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .payment-qr::before {
            content: '';
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid white;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }

        .payment-icon:hover .payment-qr {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(-10px);
        }

        .payment-qr img {
            width: 100%;
            height: 100%;
            border-radius: 5px;
            object-fit: cover;
        }

        .payment-qr .qr-title {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .payment-icons {
                bottom: 55px;
                right: 15px;
            }

            .payment-icon {
                width: 50px;
                height: 50px;
            }

            .payment-icon img {
                width: 30px;
                height: 30px;
            }

            .payment-qr {
                width: 150px;
                height: 150px;
                right: 65px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-hearts" id="hearts"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">💖 感谢捐赠者 💖</h1>
            <p class="subtitle">您的每一份爱心，都是我们前进的动力</p>
            <p class="thanks-message">
                感谢每一位慷慨解囊的朋友们！您的支持让我能够持续为大家提供更好的服务。<br>
                您的每一份捐赠我都将倍加珍惜，用心回报。
                愿所有的美好都与您相伴！
            </p>
        </div>

        <!-- 第一名 -->
        <div class="rank-1">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/GIF/lyyme.gif" alt="流云墨雨" class="avatar">
            <!-- <div class="default-avatar">w</div> -->
            <span class="crown">👑</span>
            <div class="donor-info">
                <span class="donor-name">流云雨墨</span>
                <div class="donor-amount">捐赠金额：¥60</div>
            </div>
        </div>

        <!-- 第二名 -->
        <div class="rank-2">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/GIF/lifenv.gif" alt="璃风" class="avatar">
            <!-- <div class="default-avatar">璃</div> -->
            <span class="rank-number">2</span>
            <div class="donor-info">
                <span class="donor-name">璃风</span>
                <div class="donor-amount">捐赠金额：¥50</div>
            </div>
        </div>

        <!-- 第三名 -->
        <div class="rank-3">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <img src="https://nos.zhiqiao.dpdns.org/image/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com%2Fc5f193f6-61cd-47c5-82f2-ac6fc975398b%2Faf9d1905-1b22-4d92-9c28-264cfc3aa81d%2FImage-00-33-08.png?table=block&id=169a5a8a-afda-8039-ac74-eca9c5c28cd4&t=169a5a8a-afda-8039-ac74-eca9c5c28cd4&width=330.9875183105469&cache=v2" alt="晨曦" class="avatar">
            <!-- <div class="default-avatar">晨</div> -->
            <span class="rank-number">3</span>
            <div class="donor-info">
                <span class="donor-name">晨曦</span>
                <div class="donor-amount">捐赠金额：¥30</div>
            </div>
        </div>

        <!-- 其他捐赠者 -->
        <div class="donor-item">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <!-- <img src="https://example.com/avatar5.jpg" alt="钱七" class="avatar"> -->
            <div class="default-avatar">幽</div>
            <span class="rank-number">4</span>
            <div class="donor-info">
                <span class="donor-name">幽蓝</span>
                <div class="donor-amount">捐赠金额：¥20</div>
            </div>
        </div>

        <div class="donor-item">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <!-- <img src="https://example.com/avatar7.jpg" alt="周九" class="avatar"> -->
            <div class="default-avatar">M</div>
            <span class="rank-number">5</span>
            <div class="donor-info">
                <span class="donor-name">Moonlit</span>
                <div class="donor-amount">捐赠金额：¥10</div>
            </div>
        </div>

        <div class="donor-item">
            <!-- 头像：可以使用图片链接或默认头像 -->
            <img src="https://nos.zhiqiao.dpdns.org/image/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com%2Fc5f193f6-61cd-47c5-82f2-ac6fc975398b%2Faafe74d1-0e40-4533-bc09-40bf3e36fdba%2F1012433f7cb.jpg?table=block&id=136a5a8a-afda-815b-915b-c2a2ba75c456&t=136a5a8a-afda-815b-915b-c2a2ba75c456&width=331&cache=v2" alt="吴十" class="avatar">
            <!-- <div class="default-avatar">涟</div> -->
            <span class="rank-number">6</span>
            <div class="donor-info">
                <span class="donor-name">岚涟</span>
                <div class="donor-amount">捐赠金额：¥5</div>
            </div>
        </div>
    </div>

    <!-- 收付款图标 -->
    <div class="payment-icons">
        <!-- 微信支付 -->
        <div class="payment-icon wechat-icon">
            <div class="icon-content">💬</div>
            <div class="payment-qr">
                <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/wxzf.png" alt="微信支付二维码">
                <div class="qr-title">微信支付</div>
            </div>
        </div>

        <!-- 支付宝 -->
        <div class="payment-icon alipay-icon">
            <div class="icon-content">💰</div>
            <div class="payment-qr">
                <img src="https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/image/fuk.png" alt="支付宝二维码">
                <div class="qr-title">支付宝</div>
            </div>
        </div>
    </div>

    <script>
        // 创建飘落的爱心效果
        function createHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDuration = (Math.random() * 3 + 3) + 's';
            heart.style.fontSize = (Math.random() * 10 + 15) + 'px';
            
            document.getElementById('hearts').appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 6000);
        }

        // 每隔一段时间创建一个爱心
        setInterval(createHeart, 800);

        // 页面加载完成后的欢迎效果
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 1s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
