/* eslint-disable no-undef */
import { siteConfig } from '@/lib/config'
import { useGlobal } from '@/lib/global'
import { isMobile, loadExternalResource } from '@/lib/utils'
import { useEffect, useRef } from 'react'

export default function Live2D() {
  const { theme } = useGlobal()
  const showPet = JSON.parse(siteConfig('WIDGET_PET'))
  const petLink = siteConfig('WIDGET_PET_LINK')

  const audioRef = useRef(null)
  const lastPlayedIndexRef = useRef(-1)
  const petLoadedRef = useRef(false)
  const scriptLoadedRef = useRef(false)

  const audioUrls = [
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_Rhand.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_body.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/tap_Lhand.mp3',
    'https://gcore.jsdelivr.net/gh/wob-21/Cloud-storage@main/music/live2d/start.mp3'
  ]

  useEffect(() => {
    if (showPet && !isMobile() && !petLoadedRef.current) {
      const initLive2D = () => {
        try {
          if (typeof window?.loadlive2d !== 'undefined') {
            loadlive2d('live2d', petLink)
            petLoadedRef.current = true

            const live2dCanvas = document.getElementById('live2d')
            if (live2dCanvas) {
              const safeClickHandler = () => {
                try {
                  playRandomAudio()
                } catch (e) {
                  console.warn('播放音效失败:', e)
                }
              }
              live2dCanvas.removeEventListener('click', safeClickHandler)
              live2dCanvas.addEventListener('click', safeClickHandler)
            }
          }
        } catch (err) {
          console.error('Live2D 初始化失败:', err)
        }
      }

      if (typeof window?.loadlive2d !== 'undefined') {
        initLive2D()
      } else {
        if (!scriptLoadedRef.current) {
          scriptLoadedRef.current = true
          loadExternalResource('https://cdn.jsdelivr.net/gh/stevenjoezhang/live2d-widget@latest/live2d.min.js', 'js')
            .then(() => {
              initLive2D()
            })
            .catch(error => {
              console.error('Live2D 脚本加载失败:', error)
            })
        }
      }
    }

    return () => {
      const live2dCanvas = document.getElementById('live2d')
      if (live2dCanvas) {
        live2dCanvas.replaceWith(live2dCanvas.cloneNode(true))
      }
    }
  }, [theme, petLink, showPet])

  function playRandomAudio() {
    if (!audioRef.current) {
      audioRef.current = new Audio()
      audioRef.current.preload = 'auto'
    }

    if (!audioRef.current.paused) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }

    let randomIndex
    if (audioUrls.length > 1) {
      do {
        randomIndex = Math.floor(Math.random() * audioUrls.length)
      } while (randomIndex === lastPlayedIndexRef.current)
    } else {
      randomIndex = 0
    }

    // 添加音频加载错误处理
    const tryPlayAudio = (url) => {
      audioRef.current.src = url
      audioRef.current.volume = 1.0
      lastPlayedIndexRef.current = randomIndex

      audioRef.current.onerror = () => {
        console.warn('音频加载失败:', url)
        // 尝试备用CDN
        const backupUrl = url.replace('cdn.jsdelivr.net', 'gcore.jsdelivr.net')
        if (backupUrl !== url) {
          audioRef.current.src = backupUrl
        }
      }

      audioRef.current.play().catch(err => {
        console.warn('自动播放被浏览器阻止:', err)
      })
    }

    tryPlayAudio(audioUrls[randomIndex])
  }

  if (!showPet) return null

  return (
    <>
      <canvas
        id='live2d'
        width='280'
        height='250'
        className='cursor-grab'
        onMouseDown={e => e.target.classList.add('cursor-grabbing')}
        onMouseUp={e => e.target.classList.remove('cursor-grabbing')}
      />
      <audio ref={audioRef} preload='auto' style={{ display: 'none' }} />
    </>
  )
}
