/* 本地 Noto Serif SC 字体定义 */
@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.4.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.87.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.89.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.101.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.104.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.106.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.108.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.110.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif SC';
  src: url('/webfonts/ziti/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7TcqyWqm5Tjb17qLDam6T2isIOpAN0AzDc6KDF2jLlrDm0HbsE.111.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* 本地 Noto Sans SC 字体定义 */
@font-face {
  font-family: 'Noto Sans SC';
  src: url('/webfonts/ziti/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLjOXQ.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  overflow-x: hidden;
}

.wrapper {
  min-height: 100vh;
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  justify-content: flex-start;
  flex-direction: column;
}

.sticky-nav {
  position: sticky;
  z-index: 10;
  top: -1px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0, 1);
  border-bottom-color: transparent;
}

.sticky-nav-full {
  @apply border-b border-opacity-50 border-gray-200 dark:border-gray-600 dark:border-opacity-50;
}

.header-name {
  overflow: hidden;
}

.sticky-nav-full .nav {
  @apply text-gray-600 dark:text-gray-300;
}

nav {
  flex-wrap: wrap;
  line-height: 1.5em;
}

.article-tags::-webkit-scrollbar {
  width: 0 !important;
}

.tag-container ul::-webkit-scrollbar {
  width: 0 !important;
}

.tag-container ul {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

@media (min-width: 768px) {
  .sticky-nav-full {
    @apply max-w-full border-b border-opacity-50 border-gray-200 dark:border-gray-600 dark:border-opacity-50;
  }
  .header-name {
    display: block;
    transition: all 0.5s cubic-bezier(0.4, 0, 0, 1);
  }
  .sticky-nav-full .header-name {
    @apply dark:text-gray-300 text-gray-600;
  }
}

@supports not (backdrop-filter: none) {
  .sticky-nav {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    @apply bg-day dark:bg-gray-800;
  }
}

.shadow-card {
  box-shadow:
    rgba(0, 0, 0, 0.07) 0px 1px 2px,
    rgba(0, 0, 0, 0.07) 0px 2px 4px,
    rgba(0, 0, 0, 0.07) 0px 4px 8px,
    rgba(0, 0, 0, 0.07) 0px 8px 16px,
    rgba(0, 0, 0, 0.07) 0px 16px 32px,
    rgba(0, 0, 0, 0.07) 0px 32px 64px;
}

.gt-meta {
  @apply dark:text-gray-300;
}

#waifu {
  @apply right-auto left-0 hidden lg:block z-10 !important;
}

/* 隐藏滚动条 */
.scroll-hidden {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
  scrollbar-width: none; /* firefox */
}

.scroll-hidden::-webkit-scrollbar {
  width: 0 !important;
}

.glassmorphism {
  background: hsla(0, 0%, 100%, 0.05);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.medium-zoom-overlay {
  background: none !important;
  /* background: rgba(0, 0, 0, 0.01) none repeat scroll 0% 0% !important; */
}

.shadow-text {
  text-shadow: 0.1em 0.1em 0.2em black;
}

.notion-code-copy-button > svg {
  pointer-events: none;
}

.fireworks {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  pointer-events: none;
}

[data-waline] p {
  color: var(--waline-color);
  @apply dark:text-gray-200 !important;
}

.waline-recent-content p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.waline-recent-content .wl-emoji {
  height: 1.1rem !important;
  display: inline-block !important;
  line-height: 1.25rem !important;
  vertical-align: text-bottom !important;
}

.vcontent .wl-emoji {
  display: inline-block;
  vertical-align: baseline;
  height: 1.25em;
  margin: -0.125em 0.25em;
}

/* twikoo 评论区超链接样式 */
.tk-main a {
  @apply text-blue-700;
}

/* twikoo 内置的 element-ui 加载样式 */
.el-loading-spinner {
  @apply flex justify-center items-center;
}

/* Webmention style */
.webmention-block {
  background: rgba(0, 116, 222, 0.2);
  padding: 1rem 2rem;
  border-radius: 5px;
}

.webmention-header {
  font-style: italic;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 0.5rem;
}

.webmention-block-intro a {
  color: #0000ee;
  text-decoration: underline;
}

.webmention {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
}

.webmention-counts {
  padding: 16px 0;
  font-weight: bold;
}

.webmention-counts .count {
  font-weight: bold;
  margin-right: 0.2rem;
}

/* .webmention-counts .counts > span {
  margin-right: .8rem;
} */
.webmention-counts .counts > span:not(:last-child):after {
  content: ' • ';
}

a.avatar-wrapper {
  display: inline-block;
  width: 50px;
  height: 50px;
  position: relative;
}

.webmention-avatars .avatar-wrapper {
  margin-right: -8px;
}

.avatar {
  border-radius: 50%;
  margin: 0;
  border: 3px solid rgba(0, 116, 222, 0.5);
}

.replies {
  margin: 0;
  padding: 0;
}

.reply {
  list-style: none;
  display: flex;
  position: relative;
  padding: 0;
  align-items: flex-start;
  margin-top: 0.6rem;
}

.reply p {
  margin: 0;
}

.reply .text {
  margin-left: 1rem;
  font-size: 14px;
}

.reply-author-name {
  font-weight: 500;
}

.forbid-copy {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.writing-vertical {
  writing-mode: vertical-rl; /* 竖向排列从右向左 */
  text-orientation: upright; /* 文字方向正常 */
}

/* Chatbase 在移动端禁止遮挡 */
@media (max-width: 700px) {
  button#chatbase-bubble-button {
    margin-bottom: 42px;
    margin-right: 20px;
  }
}

img {
  display: unset;
}

.adsbygoogle {
  overflow: hidden;
}
